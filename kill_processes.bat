@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion
title Windows 10 终极自动优化工具 - 专业版

echo 正在检查管理员权限...
net session >nul 2>&1
if not %errorLevel% == 0 (
    echo.
    echo ==========================================
    echo           权限检查失败
    echo ==========================================
    echo.
    echo 此脚本需要管理员权限才能正常运行。
    echo 请右键点击脚本文件，选择"以管理员身份运行"
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

cls
echo ==========================================
echo      Windows 10 终极自动优化工具
echo              专业版
echo ==========================================
echo.
echo 管理员权限验证成功！
echo 开始全面自动优化...
echo 所有操作将自动执行。
echo.
echo 将要执行的操作：
echo 1. 系统状态检查和创建还原点
echo 2. 注册表性能优化
echo 3. 服务和内存优化
echo 4. 进程清理和UWP应用管理
echo 5. 网络性能调优
echo 6. 视觉效果和存储优化
echo 7. 快速内存释放和最终清理
echo 8. 最终状态报告
echo.
echo ==========================================
echo.
echo 按任意键开始优化，或等待5秒自动开始...
timeout /t 5 >nul

goto auto_start

:auto_start
echo [步骤 1/8] 检查初始系统状态并创建还原点...
echo.
echo 正在执行步骤1...
call :get_system_status "INITIAL"
if errorlevel 1 (
    echo 警告: 系统状态检查遇到问题，继续执行...
)
call :create_restore_point
if errorlevel 1 (
    echo 警告: 还原点创建失败，继续执行...
)
echo 步骤1完成。
echo.
echo 等待3秒后开始优化...
timeout /t 3 /nobreak >nul

echo.
echo [步骤 2/8] 应用注册表性能优化...
echo.
echo 正在执行步骤2...
call :registry_optimizations
if errorlevel 1 (
    echo 警告: 注册表优化遇到问题，继续执行...
)
echo 步骤2完成。

echo.
echo [步骤 3/8] 优化服务和内存管理...
echo.
echo 正在执行步骤3...
call :services_optimization
if errorlevel 1 (
    echo 警告: 服务优化遇到问题，继续执行...
)
echo 步骤3完成。

echo.
echo [步骤 4/8] 开始进程清理和UWP应用管理...
echo.
echo 正在执行步骤4...
call :process_cleanup_auto
if errorlevel 1 (
    echo 警告: 进程清理遇到问题，继续执行...
)
echo 步骤4完成。

echo.
echo [步骤 5/8] 应用网络性能调优...
echo.
echo 正在执行步骤5...
call :network_optimization
if errorlevel 1 (
    echo 警告: 网络优化遇到问题，继续执行...
)
echo 步骤5完成。

echo.
echo [步骤 6/8] 优化视觉效果和存储...
echo.
echo 正在执行步骤6...
call :visual_storage_optimization
if errorlevel 1 (
    echo 警告: 视觉效果优化遇到问题，继续执行...
)
echo 步骤6完成。

echo.
echo [步骤 7/8] 执行快速内存释放和最终清理...
echo.
echo 正在执行步骤7...
call :quick_release_auto
if errorlevel 1 (
    echo 警告: 内存释放遇到问题，继续执行...
)
echo 步骤7完成。

echo.
echo [步骤 8/8] 检查最终系统状态...
echo.
echo 正在执行步骤8...
call :get_system_status "FINAL"
if errorlevel 1 (
    echo 警告: 最终状态检查遇到问题，继续执行...
)
echo 步骤8完成。

echo.
echo ==========================================
echo        全面优化已完成
echo ==========================================
echo.
call :show_optimization_summary
echo.
echo 窗口将在15秒后自动关闭...
echo 按任意键立即关闭。
timeout /t 15 >nul
exit /b 0

:process_cleanup_auto
echo ==========================================
echo       进程清理和UWP应用管理
echo ==========================================
echo.

REM Define target processes (expanded list)
set "PROCESSES=chrome.exe msedge.exe firefox.exe qq.exe wechat.exe douyin.exe backgroundTaskHost.exe PotPlayerMini64.exe wpscloudsvr.exe taskmgr.exe 360se.exe SGTool.exe qdownloader32.exe SeAppService.exe QQLive.exe hardwarecheck.exe QQLiveService.exe TMPThumb.exe QQLiveBrowser.exe sesvr.exe crashpad_handler.exe quark.exe 360CloudEnterpriseSync.exe KuGou.exe wps.exe"

echo 检查并终止目标进程...
echo.

set count=0
for %%p in (%PROCESSES%) do (
    tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
    if not errorlevel 1 (
        echo 正在终止 %%p...
        taskkill /f /im "%%p" >nul 2>&1
        if not errorlevel 1 (
            echo   [成功] %%p 已终止
            set /a count+=1
        ) else (
            echo   [失败] 无法终止 %%p
        )
    ) else (
        echo   [信息] %%p 未运行
    )
)

echo.
echo 已终止 %count% 个进程。
echo.

echo 清理不必要的UWP应用程序...
powershell -Command "Get-AppxPackage *3dbuilder* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowsalarms* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowscommunicationsapps* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *officehub* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *skypeapp* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *getstarted* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *zunemusic* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowsmaps* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *solitairecollection* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingfinance* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *zunevideo* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingnews* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *people* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingsports* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *soundrecorder* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingweather* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *xboxapp* | Remove-AppxPackage" >nul 2>&1
echo   [成功] UWP应用程序清理完成
echo.

echo 清理临时文件...
del /f /q "%temp%\*.*" >nul 2>&1
del /f /q "%tmp%\*.*" >nul 2>&1
del /f /q "C:\Windows\Temp\*.*" >nul 2>&1
del /f /q "C:\Windows\Prefetch\*.*" >nul 2>&1
echo 临时文件清理完成。

echo.
echo 清理系统缓存...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
nbtstat -R >nul 2>&1
echo 系统缓存清理完成。

echo.
echo 优化系统服务...
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
echo 非必要服务优化完成。

echo.
echo 检查内存使用情况...
echo   正在分析内存状态...

REM 尝试获取内存信息，如果失败则跳过
set "mem_available=1"
wmic OS get TotalVisibleMemorySize /value 2>nul | find "=" >nul
if errorlevel 1 set "mem_available=0"

if "%mem_available%"=="1" (
    for /f "tokens=2 delims==" %%a in ('wmic OS get TotalVisibleMemorySize /value 2^>nul ^| find "TotalVisibleMemorySize="') do set total_mem=%%a
    for /f "tokens=2 delims==" %%a in ('wmic OS get FreePhysicalMemory /value 2^>nul ^| find "FreePhysicalMemory="') do set free_mem=%%a

    if defined total_mem if defined free_mem (
        set /a used_mem=%total_mem%-%free_mem%
        set /a usage_percent=%used_mem%*100/%total_mem%
        echo   当前内存使用率: %usage_percent%%%

        if %usage_percent% gtr 80 (
            echo   检测到高内存使用率。执行额外清理...
            powershell -Command "[System.GC]::Collect()" >nul 2>&1
            echo   内存优化完成。
        ) else (
            echo   内存使用率正常。
        )
    ) else (
        echo   内存信息获取失败，执行基础清理...
        powershell -Command "[System.GC]::Collect()" >nul 2>&1
    )
) else (
    echo   内存检查工具不可用，执行基础清理...
    powershell -Command "[System.GC]::Collect()" >nul 2>&1
)

echo.
echo 进程清理完成！
return

:quick_release_auto
echo ==========================================
echo         快速内存释放
echo ==========================================
echo.

echo 执行快速内存释放...
echo.

echo 步骤 1: 强制垃圾回收...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
echo   内存垃圾回收完成。

echo 步骤 2: 清理剩余临时文件...
del /f /q "%temp%\*.*" >nul 2>&1
del /f /q "%tmp%\*.*" >nul 2>&1
del /f /q "C:\Windows\Temp\*.*" >nul 2>&1
echo   临时文件清理完成。

echo 步骤 3: 刷新网络缓存...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
nbtstat -R >nul 2>&1
echo   网络缓存刷新完成。

echo 步骤 4: 工作集优化...
powershell -Command "Get-Process | ForEach-Object { try { $_.ProcessorAffinity = $_.ProcessorAffinity } catch {} }" >nul 2>&1
echo   工作集优化完成。

echo 步骤 5: 内存碎片整理...
powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; Get-Process | ForEach-Object { try { $api::SetProcessWorkingSetSize($_.Handle, -1, -1) } catch {} }" >nul 2>&1
echo   内存碎片整理完成。

echo.
echo 快速内存释放完成！
return

:get_system_status
set status_type=%~1
echo ==========================================
echo        系统状态信息 (%status_type%)
echo ==========================================
echo.

echo 收集系统信息...
echo.

echo [操作系统]
echo   操作系统: %OS%
echo   计算机名: %COMPUTERNAME%

echo.
echo [处理器信息]
if defined PROCESSOR_IDENTIFIER (
    echo   处理器: %PROCESSOR_IDENTIFIER%
) else (
    echo   处理器: %PROCESSOR_ARCHITECTURE%
)

echo.
echo [内存信息]
REM 使用更简单的方法获取内存信息
echo   正在检查内存使用情况...

REM 尝试获取内存信息，如果失败则跳过
set "mem_check_failed=0"
wmic OS get TotalVisibleMemorySize /value 2>nul | find "=" >nul
if errorlevel 1 set "mem_check_failed=1"

if "%mem_check_failed%"=="0" (
    for /f "tokens=2 delims==" %%a in ('wmic OS get TotalVisibleMemorySize /value 2^>nul ^| find "TotalVisibleMemorySize="') do set total_mem=%%a
    for /f "tokens=2 delims==" %%a in ('wmic OS get FreePhysicalMemory /value 2^>nul ^| find "FreePhysicalMemory="') do set free_mem=%%a

    if defined total_mem if defined free_mem (
        set /a used_mem=%total_mem%-%free_mem%
        set /a usage_percent=%used_mem%*100/%total_mem%
        set /a free_gb=%free_mem%/1024/1024
        set /a used_gb=%used_mem%/1024/1024

        echo   已用内存: !used_gb! GB
        echo   可用内存: !free_gb! GB
        echo   使用率: !usage_percent!%%

        if "%status_type%"=="INITIAL" (
            set initial_usage=!usage_percent!
            echo   状态: 初始测量
        ) else if "%status_type%"=="FINAL" (
            if defined initial_usage (
                set /a improvement=!initial_usage!-!usage_percent!
                if !improvement! gtr 0 (
                    echo   状态: 已优化 - 内存使用率降低了 !improvement! 个百分点
                ) else (
                    echo   状态: 稳定 - 内存使用率保持稳定
                )
            ) else (
                echo   状态: 最终测量完成
            )
        )

        if !usage_percent! gtr 80 (
            echo   级别: 警告 - 内存使用率较高
        ) else if !usage_percent! gtr 70 (
            echo   级别: 注意 - 内存使用率偏高
        ) else (
            echo   级别: 正常 - 内存使用率可接受
        )
    ) else (
        echo   内存信息: 无法获取详细信息
    )
) else (
    echo   内存信息: 检查工具不可用，跳过详细检查
)

echo.
echo [优化状态]
if "%status_type%"=="FINAL" (
    echo   注册表优化: 已应用
    echo   服务优化: 已完成
    echo   内存管理: 已配置
    echo   网络性能: 已调优
    echo   视觉效果: 已优化
    echo   存储清理: 已完成
    echo   UWP应用: 已清理
    echo   计划任务: 已优化
)

echo.
return

:create_restore_point
echo 创建系统还原点...
powershell -Command "Checkpoint-Computer -Description 'Windows10终极自动优化' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
if %errorlevel% == 0 (
    echo   [成功] 系统还原点创建成功
) else (
    echo   [信息] 无法创建还原点（可能已禁用）
)
return

:registry_optimizations
echo ==========================================
echo         注册表性能优化
echo ==========================================
echo.

echo 应用网络性能调优...
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DisableBandwidthThrottling /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v FileInfoCacheEntriesMax /t REG_DWORD /d 1024 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DirectoryCacheEntriesMax /t REG_DWORD /d 1024 /f >nul 2>&1
echo   [成功] 网络性能已优化

echo 应用内存管理优化...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v ClearPageFileAtShutdown /t REG_DWORD /d 0 /f >nul 2>&1
echo   [成功] 内存管理已优化

echo 应用系统响应性调优...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 38 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v SystemResponsiveness /t REG_DWORD /d 10 /f >nul 2>&1
echo   [成功] 系统响应性已改善

echo 应用文件系统优化...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsDisableLastAccessUpdate /t REG_DWORD /d 1 /f >nul 2>&1
echo   [成功] 文件系统已优化

echo 禁用不必要的报告...
reg add "HKLM\SOFTWARE\Microsoft\Windows\Windows Error Reporting" /v Disabled /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\SQMClient\Windows" /v CEIPEnable /t REG_DWORD /d 0 /f >nul 2>&1
echo   [成功] 错误报告和遥测已优化

echo.
echo 注册表优化完成！
return

:services_optimization
echo ==========================================
echo        服务和内存优化
echo ==========================================
echo.

echo 检测系统配置...
echo   正在分析系统内存配置...

REM 使用更简单的方法检测内存，避免wmic问题
set "total_gb=8"
wmic computersystem get TotalPhysicalMemory /value 2>nul | find "=" >nul
if not errorlevel 1 (
    for /f "tokens=2 delims==" %%a in ('wmic computersystem get TotalPhysicalMemory /value 2^>nul ^| find "TotalPhysicalMemory="') do (
        set total_physical=%%a
        set /a total_gb=!total_physical!/1024/1024/1024
    )
)
echo   系统内存: %total_gb% GB

echo 优化Windows服务...
sc config "SysMain" start= disabled >nul 2>&1
sc config "WSearch" start= disabled >nul 2>&1
sc config "DiagTrack" start= disabled >nul 2>&1
sc config "dmwappushservice" start= disabled >nul 2>&1
sc config "MapsBroker" start= disabled >nul 2>&1
sc config "lfsvc" start= disabled >nul 2>&1
sc config "XblAuthManager" start= disabled >nul 2>&1
sc config "XblGameSave" start= disabled >nul 2>&1
sc config "XboxNetApiSvc" start= disabled >nul 2>&1

net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
net stop "dmwappushservice" >nul 2>&1
net stop "MapsBroker" >nul 2>&1
echo   [成功] 非必要服务已优化

echo 根据系统内存配置虚拟内存...
if %total_gb% geq 16 (
    set /a min_pagefile=16384
    set /a max_pagefile=24576
    echo   [信息] 检测到高内存系统 - 为16GB+优化
) else if %total_gb% geq 8 (
    set /a min_pagefile=12288
    set /a max_pagefile=16384
    echo   [信息] 检测到中等内存系统 - 为8-16GB优化
) else (
    set /a min_pagefile=8192
    set /a max_pagefile=12288
    echo   [信息] 检测到标准内存系统 - 为小于8GB优化
)

reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys %min_pagefile% %max_pagefile%" /f >nul 2>&1
echo   [成功] 虚拟内存已配置

if %total_gb% geq 8 (
    echo 为高内存系统禁用内存压缩...
    powershell -Command "try { Disable-MMAgent -MemoryCompression -ErrorAction SilentlyContinue } catch { }" >nul 2>&1
    echo   [成功] 内存压缩已禁用
)

echo.
echo 服务和内存优化完成！
return

:network_optimization
echo ==========================================
echo         网络性能调优
echo ==========================================
echo.

echo 应用TCP/IP优化...
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
netsh int tcp set global netdma=enabled >nul 2>&1
echo   [成功] TCP/IP堆栈已优化

echo 配置DNS以提高性能...
netsh interface ip set dns "Local Area Connection" static ******* primary >nul 2>&1
netsh interface ip add dns "Local Area Connection" ******* index=2 >nul 2>&1
echo   [成功] DNS已优化 (Google DNS)

echo 应用高级网络注册表调优...
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TcpAckFrequency /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TCPNoDelay /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v DefaultTTL /t REG_DWORD /d 64 /f >nul 2>&1
echo   [成功] 高级网络设置已应用

echo.
echo 网络性能调优完成！
return

:visual_storage_optimization
echo ==========================================
echo      视觉效果和存储优化
echo ==========================================
echo.

echo 为性能优化视觉效果...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v VisualFXSetting /t REG_DWORD /d 2 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v DragFullWindows /t REG_SZ /d 0 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v MenuShowDelay /t REG_SZ /d 0 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop\WindowMetrics" /v MinAnimate /t REG_SZ /d 0 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v TaskbarAnimations /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 0 /f >nul 2>&1
echo   [成功] 视觉效果已优化

echo 禁用Windows搜索索引...
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowIndexingEncryptedStoresOrItems /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowSearchToUseLocation /t REG_DWORD /d 0 /f >nul 2>&1
echo   [成功] 搜索索引已优化

echo 禁用休眠以释放磁盘空间...
powershell -Command "powercfg -h off" >nul 2>&1
echo   [成功] 休眠已禁用

echo 清理Windows更新缓存...
net stop wuauserv >nul 2>&1
del /q /f /s "C:\Windows\SoftwareDistribution\Download\*" >nul 2>&1
net start wuauserv >nul 2>&1
echo   [成功] Windows更新缓存已清理

echo 优化计划任务...
schtasks /change /tn "Microsoft\Windows\Application Experience\Microsoft Compatibility Appraiser" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\Consolidator" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\DiskDiagnostic\Microsoft-Windows-DiskDiagnosticDataCollector" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Windows Error Reporting\QueueReporting" /disable >nul 2>&1
echo   [成功] 计划任务已优化

echo.
echo 视觉效果和存储优化完成！
return

:show_optimization_summary
echo 性能优化摘要：
echo ✓ 系统还原点已创建
echo ✓ 注册表性能调优已应用
echo ✓ 服务已优化，内存管理已配置
echo ✓ 进程清理和UWP应用已管理
echo ✓ 网络性能已调优
echo ✓ 视觉效果已为性能优化
echo ✓ 存储优化和清理已完成
echo ✓ 内存释放和最终清理已执行
echo.
echo 您的系统已全面优化！
echo 所有操作均已通过自动执行成功完成。
return
