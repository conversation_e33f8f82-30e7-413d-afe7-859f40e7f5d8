@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title Windows 10 系统优化集成工具 - 管理员模式

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [√] 已获取管理员权限
    goto :main_menu
) else (
    echo [!] 需要管理员权限运行此脚本
    echo [*] 正在请求管理员权限...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~dpnx0\"' -Verb RunAs"
    exit /b
)

:main_menu
cls
echo ==========================================
echo      Windows 10 系统优化集成工具 v2.0
echo ==========================================
echo.
echo [功能菜单] 请选择要执行的操作：
echo.
echo 1. 进程终止 + 系统清理 + 内存优化 (原功能增强版)
echo 2. 实时内存监控和紧急释放
echo 3. 一次性防卡死系统配置
echo 4. 快速内存释放 (紧急使用)
echo 5. 查看系统状态信息
echo 6. 退出程序
echo.
echo ==========================================
set /p choice="请输入选项 (1-6): "

if "%choice%"=="1" goto :process_cleanup
if "%choice%"=="2" goto :memory_monitor
if "%choice%"=="3" goto :anti_freeze_config
if "%choice%"=="4" goto :quick_memory_release
if "%choice%"=="5" goto :system_status
if "%choice%"=="6" goto :end
echo [错误] 无效选项，请重新选择...
timeout /t 2 /nobreak >nul
goto :main_menu

:process_cleanup
cls
echo ==========================================
echo        进程终止 + 系统清理 + 内存优化
echo ==========================================
echo.

REM ====== 在这里配置要终止的程序列表 ======
REM 请根据需要修改以下程序列表
REM 每个程序用空格分隔，确保包含.exe扩展名

set "TARGET_PROCESSES=backgroundTaskHost.exe msedge.exe PotPlayerMini64.exe wpscloudsvr.exe taskmgr.exe 360se.exe SGTool.exe qdownloader32.exe SeAppService.exe QQLive.exe hardwarecheck.exe QQLiveService.exe TMPThumb.exe QQLiveBrowser.exe wpscloudsvr.exe sesvr.exe crashpad_handler.exe quark.exe 360CloudEnterpriseSync.exe KuGou.exe wps.exe qq.exe douyin.exe douyin_tray.exe i4Tools.exe updater.exe i4Service.exe i4ToolsService.exe sppsvc.exe chrome.exe TMPThumb.exe MOM.exe crashpad_handler.exe"

REM 常用浏览器（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% chrome.exe firefox.exe msedge.exe"

REM 办公软件（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% winword.exe excel.exe powerpnt.exe"

REM 聊天软件（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% qq.exe wechat.exe"

REM 游戏平台（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% steam.exe"

REM 媒体播放器（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% vlc.exe wmplayer.exe"

REM 自定义程序（请在下面添加你需要终止的其他程序）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% your_program.exe"

REM ============================================

echo [*] 目标进程列表:
set count=0
for %%p in (%TARGET_PROCESSES%) do (
    set /a count+=1
    echo     !count!. %%p
)
echo.

if %count%==0 (
    echo [!] 错误: 没有配置任何目标进程
    goto :end
)

echo ==========================================
echo [*] 开始强制终止进程...
echo ==========================================
echo.

set "killed_count=0"
set "not_found_count=0"
set "failed_count=0"

for %%p in (%TARGET_PROCESSES%) do (
    echo [处理] 检查进程: %%p
    
    REM 检查进程是否存在
    tasklist /fi "imagename eq %%p" 2>nul | find /i /c "%%p" >nul
    if errorlevel 1 (
        echo     └─ [o] 进程未运行
        set /a not_found_count+=1
    ) else (
        echo     └─ [!] 发现运行中的进程，正在强制终止...
        
        REM 使用多种方法确保进程被终止
        
        REM 方法1: 使用taskkill强制终止
        taskkill /f /im "%%p" >nul 2>&1
        
        REM 方法2: 使用wmic强制终止（备用方法）
        wmic process where "name='%%p'" delete >nul 2>&1
        
        REM 等待一秒后检查是否成功终止
        timeout /t 1 /nobreak >nul
        
        REM 验证进程是否已终止
        tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
        if errorlevel 1 (
            echo     └─ [√] 成功终止进程
            set /a killed_count+=1
        ) else (
            echo     └─ [×] 终止失败，进程仍在运行
            set /a failed_count+=1
            
            REM 最后尝试：使用PowerShell强制终止
            echo     └─ [*] 尝试PowerShell强制终止...
            powershell -Command "Get-Process -Name '%%~np' -ErrorAction SilentlyContinue | Stop-Process -Force" >nul 2>&1
            
            timeout /t 1 /nobreak >nul
            tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
            if errorlevel 1 (
                echo     └─ [√] PowerShell终止成功
                set /a killed_count+=1
                set /a failed_count-=1
            ) else (
                echo     └─ [×] 所有方法均失败
            )
        )
    )
    echo.
)

echo ==========================================
echo [*] 操作完成统计:
echo ==========================================
echo [√] 成功终止: %killed_count% 个进程
echo [o] 未发现运行: %not_found_count% 个进程  
echo [×] 终止失败: %failed_count% 个进程
echo [*] 总计处理: %count% 个进程
echo ==========================================

if %failed_count% gtr 0 (
    echo.
    echo [!] 注意: 有 %failed_count% 个进程终止失败
    echo [*] 可能原因:
    echo     1. 进程受到系统保护
    echo     2. 进程具有更高权限
    echo     3. 进程名称不正确
    echo     4. 进程正在被其他程序使用
)

echo.
echo ==========================================
echo [*] 开始系统垃圾和缓存清理...
echo ==========================================
echo.

REM 清理临时文件
echo [清理] 正在清理临时文件...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "%tmp%\*.*" >nul 2>&1
for /d %%d in ("%temp%\*") do rd /s /q "%%d" >nul 2>&1
for /d %%d in ("%tmp%\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 临时文件清理完成

REM 清理系统临时文件
echo [清理] 正在清理系统临时文件...
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Temp\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 系统临时文件清理完成

REM 清理预读文件
echo [清理] 正在清理预读文件...
del /f /s /q "C:\Windows\Prefetch\*.*" >nul 2>&1
echo     └─ [√] 预读文件清理完成

REM 清理回收站
echo [清理] 正在清理回收站...
rd /s /q "C:\$Recycle.Bin" >nul 2>&1
echo     └─ [√] 回收站清理完成

REM 清理DNS缓存
echo [清理] 正在清理DNS缓存...
ipconfig /flushdns >nul 2>&1
echo     └─ [√] DNS缓存清理完成

REM 清理ARP缓存
echo [清理] 正在清理ARP缓存...
arp -d * >nul 2>&1
echo     └─ [√] ARP缓存清理完成

REM 清理NetBIOS缓存
echo [清理] 正在清理NetBIOS缓存...
nbtstat -R >nul 2>&1
nbtstat -RR >nul 2>&1
echo     └─ [√] NetBIOS缓存清理完成

REM 清理浏览器缓存目录
echo [清理] 正在清理浏览器缓存...

REM Chrome缓存
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM Edge缓存
if exist "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM 360浏览器缓存
if exist "%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM QQ浏览器缓存
if exist "%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

echo     └─ [√] 浏览器缓存清理完成

REM 清理Windows更新缓存
echo [清理] 正在清理Windows更新缓存...
del /f /s /q "C:\Windows\SoftwareDistribution\Download\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\SoftwareDistribution\Download\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] Windows更新缓存清理完成

REM 清理字体缓存
echo [清理] 正在清理字体缓存...
del /f /s /q "C:\Windows\ServiceProfiles\LocalService\AppData\Local\FontCache\*.*" >nul 2>&1
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Fonts\*.*" >nul 2>&1
echo     └─ [√] 字体缓存清理完成

REM 清理缩略图缓存
echo [清理] 正在清理缩略图缓存...
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\thumbcache_*.db" >nul 2>&1
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\iconcache_*.db" >nul 2>&1
echo     └─ [√] 缩略图缓存清理完成

REM 清理日志文件
echo [清理] 正在清理系统日志文件...
del /f /s /q "C:\Windows\Logs\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Logs\*") do rd /s /q "%%d" >nul 2>&1
del /f /s /q "C:\Windows\Debug\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Debug\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 系统日志文件清理完成

REM 清理内存转储文件
echo [清理] 正在清理内存转储文件...
del /f /q "C:\Windows\MEMORY.DMP" >nul 2>&1
del /f /q "C:\Windows\Minidump\*.*" >nul 2>&1
echo     └─ [√] 内存转储文件清理完成

REM 清理空文件夹
echo [清理] 正在清理空文件夹...
for /f "delims=" %%d in ('dir "C:\Windows\Temp" /ad /b 2^>nul') do rd "C:\Windows\Temp\%%d" >nul 2>&1
for /f "delims=" %%d in ('dir "%temp%" /ad /b 2^>nul') do rd "%temp%\%%d" >nul 2>&1
echo     └─ [√] 空文件夹清理完成

echo.
echo ==========================================
echo [*] 开始内存优化和防卡死处理...
echo ==========================================
echo.

REM 检查当前内存使用情况
echo [检测] 正在检查内存使用情况...
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
echo     └─ [*] 内存使用率: %memory_usage_percent%%%
echo.

REM 如果内存使用率超过80%，执行紧急清理
if %memory_usage_percent% gtr 80 (
    echo [警告] 内存使用率过高（%memory_usage_percent%%%），执行紧急优化...

    REM 强制释放工作集内存
    echo [优化] 正在强制释放工作集内存...
    powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
    echo     └─ [√] 工作集内存释放完成

    REM 清理系统文件缓存
    echo [优化] 正在清理系统文件缓存...
    powershell -Command "Clear-RecycleBin -Force -ErrorAction SilentlyContinue" >nul 2>&1
    echo     └─ [√] 系统文件缓存清理完成

    REM 释放待机内存
    echo [优化] 正在释放待机内存...
    powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; $api::SetProcessWorkingSetSize((Get-Process -Id $pid).Handle, -1, -1)" >nul 2>&1
    echo     └─ [√] 待机内存释放完成
)

REM 优化虚拟内存设置
echo [优化] 正在优化虚拟内存设置...
REM 设置虚拟内存为系统管理大小
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys 0 0" /f >nul 2>&1
echo     └─ [√] 虚拟内存设置优化完成

REM 禁用内存压缩（可选，适用于内存充足的系统）
echo [优化] 正在优化内存压缩设置...
powershell -Command "Disable-MMAgent -MemoryCompression -ErrorAction SilentlyContinue" >nul 2>&1
echo     └─ [√] 内存压缩设置优化完成

REM 优化系统缓存策略
echo [优化] 正在优化系统缓存策略...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 1 /f >nul 2>&1
echo     └─ [√] 系统缓存策略优化完成

REM 禁用不必要的Windows服务以释放内存
echo [优化] 正在禁用高内存占用服务...
sc config "SysMain" start= disabled >nul 2>&1
sc config "WSearch" start= disabled >nul 2>&1
sc config "DiagTrack" start= disabled >nul 2>&1
sc config "dmwappushservice" start= disabled >nul 2>&1
sc config "MapsBroker" start= disabled >nul 2>&1
sc config "lfsvc" start= disabled >nul 2>&1
echo     └─ [√] 高内存占用服务禁用完成

REM 停止当前运行的高内存占用服务
echo [优化] 正在停止高内存占用服务...
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
net stop "dmwappushservice" >nul 2>&1
echo     └─ [√] 高内存占用服务停止完成

REM 清理内存映射文件
echo [优化] 正在清理内存映射文件...
del /f /q "%SystemRoot%\System32\config\systemprofile\AppData\Local\Microsoft\Windows\WebCache\*.*" >nul 2>&1
echo     └─ [√] 内存映射文件清理完成

REM 优化进程优先级设置
echo [优化] 正在优化进程优先级设置...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 38 /f >nul 2>&1
echo     └─ [√] 进程优先级设置优化完成

REM 再次检查内存使用情况
echo.
echo [检测] 正在重新检查内存使用情况...
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set new_free_memory=%%a
set /a new_used_memory=%total_memory%-%new_free_memory%
set /a new_memory_usage_percent=%new_used_memory%*100/%total_memory%
echo     └─ [*] 优化后内存使用率: %new_memory_usage_percent%%%

if %new_memory_usage_percent% lss %memory_usage_percent% (
    set /a memory_saved=%memory_usage_percent%-%new_memory_usage_percent%
    echo     └─ [√] 内存使用率降低了 !memory_saved! 个百分点
) else (
    echo     └─ [!] 内存使用率未明显改善，建议重启系统
)

echo.
echo ==========================================
echo [*] 系统清理完成统计:
echo ==========================================
echo [√] 临时文件清理
echo [√] 系统临时文件清理
echo [√] 预读文件清理
echo [√] 回收站清理
echo [√] DNS/ARP/NetBIOS缓存清理
echo [√] 浏览器缓存清理
echo [√] Windows更新缓存清理
echo [√] 字体缓存清理
echo [√] 缩略图缓存清理
echo [√] 系统日志文件清理
echo [√] 内存转储文件清理
echo [√] 磁盘清理
echo [√] 空文件夹清理
echo [√] 内存优化和防卡死处理
echo ==========================================

echo.
echo [完成] 进程终止和系统清理完成！
echo [提示] 按任意键返回主菜单...
pause >nul
goto :main_menu

REM ==========================================
REM 功能模块2: 实时内存监控和紧急释放
REM ==========================================
:memory_monitor
cls
echo ==========================================
echo        实时内存监控和紧急释放工具
echo ==========================================
echo.
echo [提示] 按 Ctrl+C 可随时退出监控
echo [提示] 按 Q 键退出到主菜单
echo.

:monitor_loop
REM 获取内存信息
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a

REM 计算内存使用率
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
set /a total_memory_gb=%total_memory%/1024/1024
set /a free_memory_gb=%free_memory%/1024/1024
set /a used_memory_gb=%used_memory%/1024/1024

REM 显示当前状态
cls
echo ==========================================
echo        实时内存监控和紧急释放工具
echo ==========================================
echo.
echo [状态] 内存使用情况:
echo     总内存: %total_memory_gb% GB
echo     已用内存: %used_memory_gb% GB
echo     可用内存: %free_memory_gb% GB
echo     使用率: %memory_usage_percent%%%
echo.

REM 根据内存使用率显示不同的警告
if %memory_usage_percent% gtr 90 (
    echo [严重警告] 内存使用率超过90%%！系统可能即将卡死！
    echo [自动执行] 正在执行紧急内存释放...
    goto :emergency_cleanup
) else if %memory_usage_percent% gtr 80 (
    echo [警告] 内存使用率超过80%%，建议立即清理
    echo [提示] 按 'C' 键执行清理，按 'Q' 键退出，或等待5秒自动清理
    choice /c CQ /t 5 /d C /m "选择操作"
    if errorlevel 2 goto :main_menu
    if errorlevel 1 goto :emergency_cleanup
) else if %memory_usage_percent% gtr 70 (
    echo [注意] 内存使用率超过70%%，建议关注
    echo [提示] 按 'C' 键执行清理，按 'Q' 键退出，或等待10秒继续监控
    choice /c CQ /t 10 /d C /m "选择操作"
    if errorlevel 2 goto :main_menu
    if errorlevel 1 goto :emergency_cleanup
) else (
    echo [正常] 内存使用率正常
)

echo.
echo [监控] 5秒后刷新状态...（按 Q 键退出到主菜单）
choice /c Q /t 5 /d Q /m "继续监控" >nul 2>&1
if errorlevel 1 goto :main_menu
goto :monitor_loop

:emergency_cleanup
echo.
echo ==========================================
echo [紧急] 开始内存释放操作...
echo ==========================================
echo.

REM 1. 强制垃圾回收
echo [步骤1] 强制执行垃圾回收...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
echo     └─ [√] 垃圾回收完成

REM 2. 释放工作集内存
echo [步骤2] 释放所有进程工作集内存...
powershell -Command "Get-Process | ForEach-Object { try { $_.ProcessorAffinity = $_.ProcessorAffinity } catch {} }" >nul 2>&1
echo     └─ [√] 工作集内存释放完成

REM 3. 清理系统缓存
echo [步骤3] 清理系统文件缓存...
powershell -Command "Clear-RecycleBin -Force -ErrorAction SilentlyContinue" >nul 2>&1
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1
echo     └─ [√] 系统缓存清理完成

REM 4. 强制内存整理
echo [步骤4] 强制内存整理...
powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; Get-Process | ForEach-Object { try { $api::SetProcessWorkingSetSize($_.Handle, -1, -1) } catch {} }" >nul 2>&1
echo     └─ [√] 内存整理完成

REM 5. 清理DNS和网络缓存
echo [步骤5] 清理网络缓存...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
nbtstat -R >nul 2>&1
echo     └─ [√] 网络缓存清理完成

REM 6. 停止非必要服务
echo [步骤6] 临时停止非必要服务...
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
echo     └─ [√] 非必要服务停止完成

echo.
echo [完成] 紧急内存释放操作完成！
echo [提示] 等待3秒后重新检测内存状态...
timeout /t 3 /nobreak >nul

goto :monitor_loop

REM ==========================================
REM 功能模块3: 一次性防卡死系统配置
REM ==========================================
:anti_freeze_config
cls
echo ==========================================
echo        一次性防卡死系统配置工具
echo ==========================================
echo.
echo [警告] 此工具将修改系统设置以防止内存卡死
echo [提示] 建议在执行前创建系统还原点
echo.
echo 按任意键继续，或按Ctrl+C取消...
pause >nul

echo.
echo ==========================================
echo [*] 开始系统防卡死配置...
echo ==========================================
echo.

REM 1. 创建系统还原点
echo [配置1] 创建系统还原点...
powershell -Command "Checkpoint-Computer -Description 'Anti-Freeze Configuration' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
echo     └─ [√] 系统还原点创建完成

REM 2. 优化虚拟内存设置
echo [配置2] 优化虚拟内存设置...
REM 获取系统内存大小
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set total_physical=%%a
set /a total_gb=!total_physical!/1024/1024/1024
set /a min_pagefile=!total_gb!*1024
set /a max_pagefile=!total_gb!*2048

REM 设置虚拟内存为物理内存的1.5-2倍
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys !min_pagefile! !max_pagefile!" /f >nul 2>&1
echo     └─ [√] 虚拟内存设置为 !min_pagefile!MB - !max_pagefile!MB

REM 3. 禁用内存压缩（适用于8GB以上内存）
if !total_gb! geq 8 (
    echo [配置3] 禁用内存压缩（内存充足）...
    powershell -Command "Disable-MMAgent -MemoryCompression" >nul 2>&1
    echo     └─ [√] 内存压缩已禁用
) else (
    echo [配置3] 保持内存压缩（内存不足8GB）...
    echo     └─ [√] 内存压缩保持启用
)

REM 4. 优化内存管理策略
echo [配置4] 优化内存管理策略...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v ClearPageFileAtShutdown /t REG_DWORD /d 0 /f >nul 2>&1
echo     └─ [√] 内存管理策略优化完成

REM 5. 禁用SuperFetch/SysMain服务
echo [配置5] 禁用SuperFetch/SysMain服务...
sc config "SysMain" start= disabled >nul 2>&1
net stop "SysMain" >nul 2>&1
echo     └─ [√] SuperFetch/SysMain服务已禁用

REM 6. 禁用Windows Search服务
echo [配置6] 禁用Windows Search服务...
sc config "WSearch" start= disabled >nul 2>&1
net stop "WSearch" >nul 2>&1
echo     └─ [√] Windows Search服务已禁用

REM 7. 禁用诊断跟踪服务
echo [配置7] 禁用诊断跟踪服务...
sc config "DiagTrack" start= disabled >nul 2>&1
sc config "dmwappushservice" start= disabled >nul 2>&1
net stop "DiagTrack" >nul 2>&1
net stop "dmwappushservice" >nul 2>&1
echo     └─ [√] 诊断跟踪服务已禁用

REM 8. 优化进程调度
echo [配置8] 优化进程调度策略...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 38 /f >nul 2>&1
echo     └─ [√] 进程调度策略优化完成

REM 9. 禁用视觉效果以节省内存
echo [配置9] 禁用不必要的视觉效果...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v VisualFXSetting /t REG_DWORD /d 2 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v UserPreferencesMask /t REG_BINARY /d 9012038010000000 /f >nul 2>&1
echo     └─ [√] 视觉效果优化完成

REM 10. 配置系统响应性
echo [配置10] 配置系统响应性...
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v SystemResponsiveness /t REG_DWORD /d 10 /f >nul 2>&1
echo     └─ [√] 系统响应性配置完成

REM 11. 禁用不必要的启动项
echo [配置11] 禁用不必要的启动项...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "OneDrive" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SecurityHealth" /f >nul 2>&1
echo     └─ [√] 启动项优化完成

REM 12. 配置内存低阈值警告
echo [配置12] 配置内存低阈值警告...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LowMemoryThreshold /t REG_DWORD /d 64 /f >nul 2>&1
echo     └─ [√] 内存低阈值警告配置完成

REM 13. 优化文件系统缓存
echo [配置13] 优化文件系统缓存...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsDisableLastAccessUpdate /t REG_DWORD /d 1 /f >nul 2>&1
echo     └─ [√] 文件系统缓存优化完成

REM 14. 配置网络优化
echo [配置14] 配置网络优化...
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
echo     └─ [√] 网络优化配置完成

echo.
echo ==========================================
echo [*] 防卡死配置完成统计:
echo ==========================================
echo [√] 系统还原点创建
echo [√] 虚拟内存优化
echo [√] 内存压缩配置
echo [√] 内存管理策略优化
echo [√] SuperFetch/SysMain服务禁用
echo [√] Windows Search服务禁用
echo [√] 诊断跟踪服务禁用
echo [√] 进程调度策略优化
echo [√] 视觉效果优化
echo [√] 系统响应性配置
echo [√] 启动项优化
echo [√] 内存低阈值警告配置
echo [√] 文件系统缓存优化
echo [√] 网络优化配置
echo ==========================================

echo.
echo [重要] 配置完成！建议重启系统以使所有设置生效
echo [提示] 如果遇到问题，可以使用系统还原恢复设置
echo.
echo [完成] 按任意键返回主菜单...
pause >nul
goto :main_menu

REM ==========================================
REM 功能模块4: 快速内存释放
REM ==========================================
:quick_memory_release
cls
echo ==========================================
echo           快速内存释放工具
echo ==========================================
echo.
echo [提示] 此功能将快速释放内存，适用于紧急情况
echo.

REM 显示释放前的内存状态
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
set /a free_memory_gb=%free_memory%/1024/1024

echo [状态] 释放前内存使用率: %memory_usage_percent%%%
echo [状态] 释放前可用内存: %free_memory_gb% GB
echo.

echo [执行] 开始快速内存释放...

REM 1. 强制垃圾回收
echo [步骤1] 强制垃圾回收...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1

REM 2. 释放工作集内存
echo [步骤2] 释放工作集内存...
powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; Get-Process | ForEach-Object { try { $api::SetProcessWorkingSetSize($_.Handle, -1, -1) } catch {} }" >nul 2>&1

REM 3. 清理系统缓存
echo [步骤3] 清理系统缓存...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1

REM 4. 清理网络缓存
echo [步骤4] 清理网络缓存...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1

echo [完成] 快速内存释放完成！
echo.

REM 显示释放后的内存状态
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set new_free_memory=%%a
set /a new_used_memory=%total_memory%-%new_free_memory%
set /a new_memory_usage_percent=%new_used_memory%*100/%total_memory%
set /a new_free_memory_gb=%new_free_memory%/1024/1024

echo [结果] 释放后内存使用率: %new_memory_usage_percent%%%
echo [结果] 释放后可用内存: %new_free_memory_gb% GB

if %new_memory_usage_percent% lss %memory_usage_percent% (
    set /a memory_saved=%memory_usage_percent%-%new_memory_usage_percent%
    set /a memory_freed_gb=%new_free_memory_gb%-%free_memory_gb%
    echo [成功] 内存使用率降低了 !memory_saved! 个百分点
    echo [成功] 释放了约 !memory_freed_gb! GB 内存
) else (
    echo [提示] 内存使用率未明显改善
)

echo.
echo [完成] 按任意键返回主菜单...
pause >nul
goto :main_menu

REM ==========================================
REM 功能模块5: 查看系统状态信息
REM ==========================================
:system_status
cls
echo ==========================================
echo           系统状态信息查看
echo ==========================================
echo.

REM 获取系统基本信息
echo [系统信息]
for /f "tokens=2 delims=:" %%a in ('wmic OS get Caption /value ^| find "="') do echo     操作系统: %%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get Version /value ^| find "="') do echo     系统版本: %%a
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do (
    set total_physical=%%a
    set /a total_gb=!total_physical!/1024/1024/1024
    echo     物理内存: !total_gb! GB
)
echo.

REM 获取内存使用情况
echo [内存状态]
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get AvailableVirtualMemory /value ^| find "="') do set available_virtual=%%a

set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
set /a total_memory_gb=%total_memory%/1024/1024
set /a free_memory_gb=%free_memory%/1024/1024
set /a used_memory_gb=%used_memory%/1024/1024

echo     总内存: %total_memory_gb% GB
echo     已用内存: %used_memory_gb% GB
echo     可用内存: %free_memory_gb% GB
echo     使用率: %memory_usage_percent%%%

if %memory_usage_percent% gtr 90 (
    echo     状态: [严重警告] 内存使用率过高！
) else if %memory_usage_percent% gtr 80 (
    echo     状态: [警告] 内存使用率较高
) else if %memory_usage_percent% gtr 70 (
    echo     状态: [注意] 内存使用率偏高
) else (
    echo     状态: [正常] 内存使用率正常
)
echo.

REM 获取CPU使用情况
echo [CPU状态]
for /f "tokens=2 delims=:" %%a in ('wmic cpu get Name /value ^| find "="') do echo     处理器: %%a
for /f "skip=1 tokens=2 delims=," %%a in ('wmic cpu get LoadPercentage /format:csv') do (
    if not "%%a"=="" (
        echo     CPU使用率: %%a%%
        if %%a gtr 90 (
            echo     状态: [警告] CPU使用率过高
        ) else if %%a gtr 70 (
            echo     状态: [注意] CPU使用率偏高
        ) else (
            echo     状态: [正常] CPU使用率正常
        )
    )
)
echo.

REM 获取磁盘使用情况
echo [磁盘状态]
for /f "skip=1 tokens=1,2,3,4 delims=," %%a in ('wmic logicaldisk where "DriveType=3" get Size,FreeSpace,DeviceID /format:csv') do (
    if not "%%b"=="" (
        set /a disk_total=%%c/1024/1024/1024
        set /a disk_free=%%b/1024/1024/1024
        set /a disk_used=!disk_total!-!disk_free!
        set /a disk_usage_percent=!disk_used!*100/!disk_total!
        echo     驱动器 %%d: !disk_used!GB/!disk_total!GB 使用率: !disk_usage_percent!%%
    )
)
echo.

REM 获取关键服务状态
echo [关键服务状态]
for %%s in ("SysMain" "WSearch" "DiagTrack" "dmwappushservice") do (
    for /f "tokens=4" %%a in ('sc query %%s ^| find "STATE"') do (
        echo     %%s: %%a
    )
)
echo.

REM 获取启动项数量
echo [启动项信息]
for /f %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" 2^>nul ^| find /c "REG_"') do echo     当前用户启动项: %%a 个
for /f %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" 2^>nul ^| find /c "REG_"') do echo     系统启动项: %%a 个
echo.

echo ==========================================
echo [完成] 按任意键返回主菜单...
pause >nul
goto :main_menu

REM ==========================================
REM 程序结束
REM ==========================================
:end
cls
echo ==========================================
echo      Windows 10 系统优化集成工具
echo ==========================================
echo.
echo [感谢] 感谢使用本工具！
echo [提示] 如果工具对您有帮助，建议定期运行以保持系统最佳状态
echo.
echo [建议使用频率]
echo     - 进程清理: 每天或感觉卡顿时
echo     - 内存监控: 长时间使用电脑时
echo     - 系统配置: 仅需运行一次
echo     - 快速释放: 紧急情况下使用
echo.
echo 按任意键退出程序...
pause >nul
exit /b