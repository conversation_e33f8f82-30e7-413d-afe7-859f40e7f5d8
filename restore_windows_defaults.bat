@echo off
setlocal enabledelayedexpansion
title Windows 10 Optimization Restore Tool

REM Check admin privileges
net session >nul 2>&1
if not %errorLevel% == 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

cls
echo ==========================================
echo   Windows 10 Optimization Restore Tool
echo ==========================================
echo.
echo This script will restore Windows 10 to default settings
echo and undo optimizations made by the optimization scripts.
echo.
echo WARNING: This will restore system defaults and may
echo re-enable services and features that were optimized.
echo.

set /p confirm="Do you want to proceed with restoration? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled by user.
    pause
    exit /b 0
)

echo.
echo Creating restore point before restoration...
powershell -Command "Checkpoint-Computer -Description 'Before Windows Defaults Restoration' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
echo.

echo [RESTORE] Restoring registry settings to defaults...
REM Restore network settings
reg delete "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DisableBandwidthThrottling /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v FileInfoCacheEntriesMax /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DirectoryCacheEntriesMax /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v FileNotFoundCacheEntriesMax /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DormantFileLimit /f >nul 2>&1

REM Restore memory management settings
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v ClearPageFileAtShutdown /t REG_DWORD /d 0 /f >nul 2>&1

REM Restore priority control
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 2 /f >nul 2>&1

REM Restore system responsiveness
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v SystemResponsiveness /t REG_DWORD /d 20 /f >nul 2>&1

REM Restore NTFS settings
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsDisableLastAccessUpdate /t REG_DWORD /d 0 /f >nul 2>&1

REM Restore Windows Error Reporting
reg add "HKLM\SOFTWARE\Microsoft\Windows\Windows Error Reporting" /v Disabled /t REG_DWORD /d 0 /f >nul 2>&1

REM Restore Customer Experience Improvement Program
reg add "HKLM\SOFTWARE\Microsoft\SQMClient\Windows" /v CEIPEnable /t REG_DWORD /d 1 /f >nul 2>&1

echo Registry settings restored to defaults.
echo.

echo [RESTORE] Re-enabling Windows services...
REM Re-enable important services
sc config "SysMain" start= auto >nul 2>&1
sc config "WSearch" start= auto >nul 2>&1
sc config "DiagTrack" start= auto >nul 2>&1
sc config "dmwappushservice" start= auto >nul 2>&1
sc config "MapsBroker" start= demand >nul 2>&1
sc config "lfsvc" start= demand >nul 2>&1
sc config "SharedAccess" start= demand >nul 2>&1
sc config "TrkWks" start= auto >nul 2>&1
sc config "WbioSrvc" start= demand >nul 2>&1
sc config "WMPNetworkSvc" start= demand >nul 2>&1
sc config "XblAuthManager" start= demand >nul 2>&1
sc config "XblGameSave" start= demand >nul 2>&1
sc config "XboxNetApiSvc" start= demand >nul 2>&1
sc config "XboxGipSvc" start= demand >nul 2>&1
sc config "Themes" start= auto >nul 2>&1
sc config "TabletInputService" start= demand >nul 2>&1
sc config "WerSvc" start= demand >nul 2>&1
sc config "Spooler" start= auto >nul 2>&1

REM Start essential services
net start "SysMain" >nul 2>&1
net start "WSearch" >nul 2>&1
net start "Themes" >nul 2>&1
net start "Spooler" >nul 2>&1

echo Windows services restored.
echo.

echo [RESTORE] Re-enabling scheduled tasks...
REM Re-enable scheduled tasks
schtasks /change /tn "Microsoft\Windows\Application Experience\Microsoft Compatibility Appraiser" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Application Experience\ProgramDataUpdater" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Autochk\Proxy" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\Consolidator" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\KernelCeipTask" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\UsbCeip" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\DiskDiagnostic\Microsoft-Windows-DiskDiagnosticDataCollector" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Maintenance\WinSAT" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Maps\MapsToastTask" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Maps\MapsUpdateTask" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Mobile Broadband Accounts\MNO Metadata Parser" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Power Efficiency Diagnostics\AnalyzeSystem" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Windows Error Reporting\QueueReporting" /enable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\WindowsUpdate\Scheduled Start" /enable >nul 2>&1

echo Scheduled tasks restored.
echo.

echo [RESTORE] Restoring visual effects...
REM Restore visual effects
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v VisualFXSetting /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v DragFullWindows /t REG_SZ /d 1 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v MenuShowDelay /t REG_SZ /d 400 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop\WindowMetrics" /v MinAnimate /t REG_SZ /d 1 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ListviewAlphaSelect /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ListviewShadow /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v TaskbarAnimations /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\DWM" /v EnableAeroPeek /t REG_DWORD /d 1 /f >nul 2>&1

REM Restore transparency effects
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul 2>&1

echo Visual effects restored.
echo.

echo [RESTORE] Restoring Windows Search and indexing...
REM Restore Windows Search settings
reg delete "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowIndexingEncryptedStoresOrItems /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowSearchToUseLocation /f >nul 2>&1

echo Windows Search restored.
echo.

echo [RESTORE] Restoring power settings...
REM Restore balanced power plan
powercfg -setactive 381b4222-f694-41f0-9685-ff5bb260df2e >nul 2>&1

REM Re-enable hibernation
powershell -Command "powercfg -h on" >nul 2>&1

REM Restore USB selective suspend
powercfg -setacvalueindex SCHEME_CURRENT 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 1 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 1 >nul 2>&1

echo Power settings restored.
echo.

echo [RESTORE] Restoring network settings...
REM Reset TCP/IP settings
netsh int tcp reset >nul 2>&1
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1

REM Reset DNS to automatic
netsh interface ip set dns "Local Area Connection" dhcp >nul 2>&1

echo Network settings restored.
echo.

echo [RESTORE] Re-enabling memory compression...
REM Re-enable memory compression
powershell -Command "Enable-MMAgent -MemoryCompression" >nul 2>&1

echo Memory compression restored.
echo.

echo [RESTORE] Restoring advanced settings...
REM Restore advanced registry settings
reg delete "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v FeatureSettings /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v FeatureSettingsOverride /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v FeatureSettingsOverrideMask /f >nul 2>&1

REM Restore file system settings
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsDisable8dot3NameCreation /t REG_DWORD /d 2 /f >nul 2>&1
reg delete "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v LongPathsEnabled /f >nul 2>&1

echo Advanced settings restored.
echo.

echo [RESTORE] Running system maintenance...
REM Run system file checker
sfc /scannow >nul 2>&1

REM Rebuild search index
sc stop "WSearch" >nul 2>&1
del /q /f /s "C:\ProgramData\Microsoft\Search\Data\Applications\Windows\*" >nul 2>&1
sc start "WSearch" >nul 2>&1

echo System maintenance completed.
echo.

echo ==========================================
echo        RESTORATION COMPLETED
echo ==========================================
echo.
echo Windows 10 has been restored to default settings.
echo.
echo Changes made:
echo ✓ Registry settings restored
echo ✓ Services re-enabled
echo ✓ Scheduled tasks re-enabled
echo ✓ Visual effects restored
echo ✓ Windows Search restored
echo ✓ Power settings restored
echo ✓ Network settings reset
echo ✓ Memory compression re-enabled
echo ✓ Advanced settings restored
echo.
echo NOTES:
echo - Some UWP apps may need to be reinstalled from Microsoft Store
echo - Custom settings may need to be reconfigured
echo - System performance will return to default levels
echo - A restart is recommended to complete the restoration
echo.
echo ==========================================
echo.

set /p restart_choice="Would you like to restart your computer now? (Y/N): "
if /i "%restart_choice%"=="Y" (
    echo Restarting computer in 10 seconds...
    timeout /t 10 /nobreak
    shutdown /r /t 0
) else (
    echo Please restart your computer manually to complete the restoration.
    echo.
    echo Thank you for using Windows 10 Optimization Restore Tool!
    pause
)

endlocal
