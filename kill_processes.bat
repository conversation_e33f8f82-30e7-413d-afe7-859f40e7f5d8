@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title 进程强制终止工具 - 管理员模式

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [√] 已获取管理员权限
    goto :main
) else (
    echo [!] 需要管理员权限运行此脚本
    echo [*] 正在请求管理员权限...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~dpnx0\"' -Verb RunAs"
    exit /b
)

:main
cls
echo ==========================================
echo          进程强制终止工具 v1.0
echo ==========================================
echo.

REM ====== 在这里配置要终止的程序列表 ======
REM 请根据需要修改以下程序列表
REM 每个程序用空格分隔，确保包含.exe扩展名

set "TARGET_PROCESSES=backgroundTaskHost.exe msedge.exe PotPlayerMini64.exe wpscloudsvr.exe taskmgr.exe 360se.exe SGTool.exe qdownloader32.exe SeAppService.exe QQLive.exe hardwarecheck.exe QQLiveService.exe TMPThumb.exe QQLiveBrowser.exe wpscloudsvr.exe sesvr.exe crashpad_handler.exe quark.exe 360CloudEnterpriseSync.exe KuGou.exe wps.exe qq.exe douyin.exe douyin_tray.exe i4Tools.exe updater.exe i4Service.exe i4ToolsService.exe sppsvc.exe chrome.exe TMPThumb.exe MOM.exe crashpad_handler.exe"

REM 常用浏览器（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% chrome.exe firefox.exe msedge.exe"

REM 办公软件（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% winword.exe excel.exe powerpnt.exe"

REM 聊天软件（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% qq.exe wechat.exe"

REM 游戏平台（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% steam.exe"

REM 媒体播放器（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% vlc.exe wmplayer.exe"

REM 自定义程序（请在下面添加你需要终止的其他程序）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% your_program.exe"

REM ============================================

echo [*] 目标进程列表:
set count=0
for %%p in (%TARGET_PROCESSES%) do (
    set /a count+=1
    echo     !count!. %%p
)
echo.

if %count%==0 (
    echo [!] 错误: 没有配置任何目标进程
    goto :end
)

echo ==========================================
echo [*] 开始强制终止进程...
echo ==========================================
echo.

set "killed_count=0"
set "not_found_count=0"
set "failed_count=0"

for %%p in (%TARGET_PROCESSES%) do (
    echo [处理] 检查进程: %%p
    
    REM 检查进程是否存在
    tasklist /fi "imagename eq %%p" 2>nul | find /i /c "%%p" >nul
    if errorlevel 1 (
        echo     └─ [o] 进程未运行
        set /a not_found_count+=1
    ) else (
        echo     └─ [!] 发现运行中的进程，正在强制终止...
        
        REM 使用多种方法确保进程被终止
        
        REM 方法1: 使用taskkill强制终止
        taskkill /f /im "%%p" >nul 2>&1
        
        REM 方法2: 使用wmic强制终止（备用方法）
        wmic process where "name='%%p'" delete >nul 2>&1
        
        REM 等待一秒后检查是否成功终止
        timeout /t 1 /nobreak >nul
        
        REM 验证进程是否已终止
        tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
        if errorlevel 1 (
            echo     └─ [√] 成功终止进程
            set /a killed_count+=1
        ) else (
            echo     └─ [×] 终止失败，进程仍在运行
            set /a failed_count+=1
            
            REM 最后尝试：使用PowerShell强制终止
            echo     └─ [*] 尝试PowerShell强制终止...
            powershell -Command "Get-Process -Name '%%~np' -ErrorAction SilentlyContinue | Stop-Process -Force" >nul 2>&1
            
            timeout /t 1 /nobreak >nul
            tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
            if errorlevel 1 (
                echo     └─ [√] PowerShell终止成功
                set /a killed_count+=1
                set /a failed_count-=1
            ) else (
                echo     └─ [×] 所有方法均失败
            )
        )
    )
    echo.
)

echo ==========================================
echo [*] 操作完成统计:
echo ==========================================
echo [√] 成功终止: %killed_count% 个进程
echo [o] 未发现运行: %not_found_count% 个进程  
echo [×] 终止失败: %failed_count% 个进程
echo [*] 总计处理: %count% 个进程
echo ==========================================

if %failed_count% gtr 0 (
    echo.
    echo [!] 注意: 有 %failed_count% 个进程终止失败
    echo [*] 可能原因:
    echo     1. 进程受到系统保护
    echo     2. 进程具有更高权限
    echo     3. 进程名称不正确
    echo     4. 进程正在被其他程序使用
)

echo.
echo ==========================================
echo [*] 开始系统垃圾和缓存清理...
echo ==========================================
echo.

REM 清理临时文件
echo [清理] 正在清理临时文件...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "%tmp%\*.*" >nul 2>&1
for /d %%d in ("%temp%\*") do rd /s /q "%%d" >nul 2>&1
for /d %%d in ("%tmp%\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 临时文件清理完成

REM 清理系统临时文件
echo [清理] 正在清理系统临时文件...
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Temp\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 系统临时文件清理完成

REM 清理预读文件
echo [清理] 正在清理预读文件...
del /f /s /q "C:\Windows\Prefetch\*.*" >nul 2>&1
echo     └─ [√] 预读文件清理完成

REM 清理回收站
echo [清理] 正在清理回收站...
rd /s /q "C:\$Recycle.Bin" >nul 2>&1
echo     └─ [√] 回收站清理完成

REM 清理DNS缓存
echo [清理] 正在清理DNS缓存...
ipconfig /flushdns >nul 2>&1
echo     └─ [√] DNS缓存清理完成

REM 清理ARP缓存
echo [清理] 正在清理ARP缓存...
arp -d * >nul 2>&1
echo     └─ [√] ARP缓存清理完成

REM 清理NetBIOS缓存
echo [清理] 正在清理NetBIOS缓存...
nbtstat -R >nul 2>&1
nbtstat -RR >nul 2>&1
echo     └─ [√] NetBIOS缓存清理完成

REM 清理浏览器缓存目录
echo [清理] 正在清理浏览器缓存...

REM Chrome缓存
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM Edge缓存
if exist "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM 360浏览器缓存
if exist "%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM QQ浏览器缓存
if exist "%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

echo     └─ [√] 浏览器缓存清理完成

REM 清理Windows更新缓存
echo [清理] 正在清理Windows更新缓存...
del /f /s /q "C:\Windows\SoftwareDistribution\Download\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\SoftwareDistribution\Download\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] Windows更新缓存清理完成

REM 清理字体缓存
echo [清理] 正在清理字体缓存...
del /f /s /q "C:\Windows\ServiceProfiles\LocalService\AppData\Local\FontCache\*.*" >nul 2>&1
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Fonts\*.*" >nul 2>&1
echo     └─ [√] 字体缓存清理完成

REM 清理缩略图缓存
echo [清理] 正在清理缩略图缓存...
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\thumbcache_*.db" >nul 2>&1
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\iconcache_*.db" >nul 2>&1
echo     └─ [√] 缩略图缓存清理完成

REM 清理日志文件
echo [清理] 正在清理系统日志文件...
del /f /s /q "C:\Windows\Logs\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Logs\*") do rd /s /q "%%d" >nul 2>&1
del /f /s /q "C:\Windows\Debug\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Debug\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 系统日志文件清理完成

REM 清理内存转储文件
echo [清理] 正在清理内存转储文件...
del /f /q "C:\Windows\MEMORY.DMP" >nul 2>&1
del /f /q "C:\Windows\Minidump\*.*" >nul 2>&1
echo     └─ [√] 内存转储文件清理完成

REM 清理空文件夹
echo [清理] 正在清理空文件夹...
for /f "delims=" %%d in ('dir "C:\Windows\Temp" /ad /b 2^>nul') do rd "C:\Windows\Temp\%%d" >nul 2>&1
for /f "delims=" %%d in ('dir "%temp%" /ad /b 2^>nul') do rd "%temp%\%%d" >nul 2>&1
echo     └─ [√] 空文件夹清理完成

echo.
echo ==========================================
echo [*] 系统清理完成统计:
echo ==========================================
echo [√] 临时文件清理
echo [√] 系统临时文件清理  
echo [√] 预读文件清理
echo [√] 回收站清理
echo [√] DNS/ARP/NetBIOS缓存清理
echo [√] 浏览器缓存清理
echo [√] Windows更新缓存清理
echo [√] 字体缓存清理
echo [√] 缩略图缓存清理
echo [√] 系统日志文件清理
echo [√] 内存转储文件清理
echo [√] 磁盘清理
echo [√] 空文件夹清理
echo ==========================================

:end
echo.
echo 按任意键退出...
pause >nul
exit /b 