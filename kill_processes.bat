@echo off
setlocal enabledelayedexpansion
title Windows 10 Memory Optimization Tool

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Administrator privileges confirmed
    goto :main_menu
) else (
    echo [!] Administrator privileges required
    echo [*] Requesting administrator privileges...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~dpnx0\"' -Verb RunAs"
    exit /b
)

:main_menu
cls
echo ==========================================
echo    Windows 10 Memory Optimization Tool
echo ==========================================
echo.
echo [Menu] Please select an operation:
echo.
echo 1. Process Termination + System Cleanup + Memory Optimization
echo 2. Real-time Memory Monitoring and Emergency Release
echo 3. One-time Anti-freeze System Configuration
echo 4. Quick Memory Release (Emergency Use)
echo 5. View System Status Information
echo 6. Exit Program
echo.
echo ==========================================
set /p choice="Please enter option (1-6): "

if "%choice%"=="1" goto :process_cleanup
if "%choice%"=="2" goto :memory_monitor
if "%choice%"=="3" goto :anti_freeze_config
if "%choice%"=="4" goto :quick_memory_release
if "%choice%"=="5" goto :system_status
if "%choice%"=="6" goto :end
echo [Error] Invalid option, please try again...
timeout /t 2 /nobreak >nul
goto :main_menu

:process_cleanup
cls
echo ==========================================
echo   Process Termination + System Cleanup
echo ==========================================
echo.

REM Configure target processes
set "TARGET_PROCESSES=backgroundTaskHost.exe msedge.exe PotPlayerMini64.exe wpscloudsvr.exe taskmgr.exe 360se.exe SGTool.exe qdownloader32.exe SeAppService.exe QQLive.exe hardwarecheck.exe QQLiveService.exe TMPThumb.exe QQLiveBrowser.exe sesvr.exe crashpad_handler.exe quark.exe 360CloudEnterpriseSync.exe KuGou.exe wps.exe qq.exe douyin.exe douyin_tray.exe i4Tools.exe updater.exe i4Service.exe i4ToolsService.exe sppsvc.exe chrome.exe MOM.exe"

echo [*] Target process list:
set count=0
for %%p in (%TARGET_PROCESSES%) do (
    set /a count+=1
    echo     !count!. %%p
)
echo.

if %count%==0 (
    echo [!] Error: No target processes configured
    echo [Tip] Press any key to return to main menu...
    pause >nul
    goto :main_menu
)

echo ==========================================
echo [*] Starting process termination...
echo ==========================================
echo.

set "killed_count=0"
set "not_found_count=0"
set "failed_count=0"

for %%p in (%TARGET_PROCESSES%) do (
    echo [Processing] Checking process: %%p
    
    REM Check if process exists
    tasklist /fi "imagename eq %%p" 2>nul | find /i /c "%%p" >nul
    if errorlevel 1 (
        echo     └─ [o] Process not running
        set /a not_found_count+=1
    ) else (
        echo     └─ [!] Found running process, force terminating...
        
        REM Method 1: Use taskkill
        taskkill /f /im "%%p" >nul 2>&1
        
        REM Method 2: Use wmic as backup
        wmic process where "name='%%p'" delete >nul 2>&1
        
        REM Wait and verify
        timeout /t 1 /nobreak >nul
        
        tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
        if errorlevel 1 (
            echo     └─ [√] Successfully terminated process
            set /a killed_count+=1
        ) else (
            echo     └─ [×] Termination failed, process still running
            set /a failed_count+=1
        )
    )
    echo.
)

echo ==========================================
echo [*] Operation completed statistics:
echo ==========================================
echo [√] Successfully terminated: %killed_count% processes
echo [o] Not found running: %not_found_count% processes  
echo [×] Termination failed: %failed_count% processes
echo [*] Total processed: %count% processes
echo ==========================================

REM System cleanup
echo.
echo ==========================================
echo [*] Starting system cleanup...
echo ==========================================
echo.

echo [Cleanup] Cleaning temporary files...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "%tmp%\*.*" >nul 2>&1
echo     └─ [√] Temporary files cleanup completed

echo [Cleanup] Cleaning system temporary files...
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1
echo     └─ [√] System temporary files cleanup completed

echo [Cleanup] Cleaning prefetch files...
del /f /s /q "C:\Windows\Prefetch\*.*" >nul 2>&1
echo     └─ [√] Prefetch files cleanup completed

echo [Cleanup] Cleaning DNS cache...
ipconfig /flushdns >nul 2>&1
echo     └─ [√] DNS cache cleanup completed

echo [Cleanup] Cleaning ARP cache...
arp -d * >nul 2>&1
echo     └─ [√] ARP cache cleanup completed

REM Memory optimization
echo.
echo ==========================================
echo [*] Starting memory optimization...
echo ==========================================
echo.

echo [Detection] Checking memory usage...
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
echo     └─ [*] Memory usage: %memory_usage_percent%%%
echo.

if %memory_usage_percent% gtr 80 (
    echo [Warning] High memory usage (%memory_usage_percent%%%%), executing emergency optimization...
    
    echo [Optimization] Force releasing working set memory...
    powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
    echo     └─ [√] Working set memory release completed
    
    echo [Optimization] Cleaning system file cache...
    powershell -Command "Clear-RecycleBin -Force -ErrorAction SilentlyContinue" >nul 2>&1
    echo     └─ [√] System file cache cleanup completed
)

echo [Optimization] Optimizing virtual memory settings...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys 0 0" /f >nul 2>&1
echo     └─ [√] Virtual memory settings optimization completed

echo [Optimization] Disabling high memory usage services...
sc config "SysMain" start= disabled >nul 2>&1
sc config "WSearch" start= disabled >nul 2>&1
sc config "DiagTrack" start= disabled >nul 2>&1
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
echo     └─ [√] High memory usage services disabled

echo.
echo [Completed] Process termination and system cleanup completed!
echo [Tip] Press any key to return to main menu...
pause >nul
goto :main_menu

:memory_monitor
cls
echo ==========================================
echo     Real-time Memory Monitoring Tool
echo ==========================================
echo.
echo [Tip] Press Ctrl+C to exit monitoring at any time
echo.

:monitor_loop
REM Get memory information
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a

REM Calculate memory usage
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
set /a total_memory_gb=%total_memory%/1024/1024
set /a free_memory_gb=%free_memory%/1024/1024
set /a used_memory_gb=%used_memory%/1024/1024

REM Display current status
cls
echo ==========================================
echo     Real-time Memory Monitoring Tool
echo ==========================================
echo.
echo [Status] Memory usage:
echo     Total memory: %total_memory_gb% GB
echo     Used memory: %used_memory_gb% GB
echo     Free memory: %free_memory_gb% GB
echo     Usage rate: %memory_usage_percent%%%
echo.

if %memory_usage_percent% gtr 90 (
    echo [CRITICAL WARNING] Memory usage over 90%%! System may freeze soon!
    echo [Auto Execute] Executing emergency memory release...
    goto :emergency_cleanup
) else if %memory_usage_percent% gtr 80 (
    echo [WARNING] Memory usage over 80%%, immediate cleanup recommended
    echo [Tip] Auto cleanup in 5 seconds, or press Ctrl+C to exit
    timeout /t 5 /nobreak >nul 2>&1
    goto :emergency_cleanup
) else if %memory_usage_percent% gtr 70 (
    echo [NOTICE] Memory usage over 70%%, attention recommended
    echo [Tip] Continue monitoring in 10 seconds, or press Ctrl+C to exit
    timeout /t 10 /nobreak >nul 2>&1
) else (
    echo [NORMAL] Memory usage is normal
)

echo.
echo [Monitoring] Refreshing status in 5 seconds... (Press Ctrl+C to exit)
timeout /t 5 /nobreak >nul 2>&1
goto :monitor_loop

:emergency_cleanup
echo.
echo ==========================================
echo [EMERGENCY] Starting memory release...
echo ==========================================
echo.

echo [Step1] Force garbage collection...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
echo     └─ [√] Garbage collection completed

echo [Step2] Release working set memory...
powershell -Command "Get-Process | ForEach-Object { try { $_.ProcessorAffinity = $_.ProcessorAffinity } catch {} }" >nul 2>&1
echo     └─ [√] Working set memory release completed

echo [Step3] Clean system cache...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1
echo     └─ [√] System cache cleanup completed

echo [Step4] Clean network cache...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
echo     └─ [√] Network cache cleanup completed

echo.
echo [Completed] Emergency memory release completed!
echo [Tip] Rechecking memory status in 3 seconds...
timeout /t 3 /nobreak >nul

goto :monitor_loop

:anti_freeze_config
cls
echo ==========================================
echo   One-time Anti-freeze System Configuration
echo ==========================================
echo.
echo [WARNING] This tool will modify system settings to prevent memory freeze
echo [TIP] System restore point will be created before execution
echo.
echo Press any key to continue, or Ctrl+C to cancel...
pause >nul

echo.
echo ==========================================
echo [*] Starting anti-freeze configuration...
echo ==========================================
echo.

echo [Config1] Creating system restore point...
powershell -Command "Checkpoint-Computer -Description 'Anti-Freeze Configuration' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
echo     └─ [√] System restore point created

echo [Config2] Optimizing virtual memory settings...
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set total_physical=%%a
set /a total_gb=!total_physical!/1024/1024/1024
set /a min_pagefile=!total_gb!*1024
set /a max_pagefile=!total_gb!*2048
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys !min_pagefile! !max_pagefile!" /f >nul 2>&1
echo     └─ [√] Virtual memory set to !min_pagefile!MB - !max_pagefile!MB

echo [Config3] Configuring memory compression...
if !total_gb! geq 8 (
    echo     Disabling memory compression (sufficient memory)...
    powershell -Command "Disable-MMAgent -MemoryCompression" >nul 2>&1
    echo     └─ [√] Memory compression disabled
) else (
    echo     Keeping memory compression (insufficient memory)...
    echo     └─ [√] Memory compression kept enabled
)

echo [Config4] Optimizing memory management strategy...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v ClearPageFileAtShutdown /t REG_DWORD /d 0 /f >nul 2>&1
echo     └─ [√] Memory management strategy optimization completed

echo [Config5] Disabling SuperFetch/SysMain service...
sc config "SysMain" start= disabled >nul 2>&1
net stop "SysMain" >nul 2>&1
echo     └─ [√] SuperFetch/SysMain service disabled

echo [Config6] Disabling Windows Search service...
sc config "WSearch" start= disabled >nul 2>&1
net stop "WSearch" >nul 2>&1
echo     └─ [√] Windows Search service disabled

echo [Config7] Disabling diagnostic tracking services...
sc config "DiagTrack" start= disabled >nul 2>&1
sc config "dmwappushservice" start= disabled >nul 2>&1
net stop "DiagTrack" >nul 2>&1
net stop "dmwappushservice" >nul 2>&1
echo     └─ [√] Diagnostic tracking services disabled

echo [Config8] Optimizing process scheduling...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 38 /f >nul 2>&1
echo     └─ [√] Process scheduling strategy optimization completed

echo [Config9] Disabling unnecessary visual effects...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v VisualFXSetting /t REG_DWORD /d 2 /f >nul 2>&1
echo     └─ [√] Visual effects optimization completed

echo [Config10] Configuring system responsiveness...
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v SystemResponsiveness /t REG_DWORD /d 10 /f >nul 2>&1
echo     └─ [√] System responsiveness configuration completed

echo.
echo ==========================================
echo [*] Anti-freeze configuration completed!
echo ==========================================
echo [IMPORTANT] Configuration completed! Restart system for all settings to take effect
echo [TIP] If problems occur, use system restore to recover settings
echo.
echo [Completed] Press any key to return to main menu...
pause >nul
goto :main_menu

:quick_memory_release
cls
echo ==========================================
echo         Quick Memory Release Tool
echo ==========================================
echo.
echo [TIP] This function will quickly release memory, suitable for emergency situations
echo.

REM Display memory status before release
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
set /a free_memory_gb=%free_memory%/1024/1024

echo [Status] Memory usage before release: %memory_usage_percent%%%
echo [Status] Available memory before release: %free_memory_gb% GB
echo.

echo [Execute] Starting quick memory release...

echo [Step1] Force garbage collection...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1

echo [Step2] Release working set memory...
powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; Get-Process | ForEach-Object { try { $api::SetProcessWorkingSetSize($_.Handle, -1, -1) } catch {} }" >nul 2>&1

echo [Step3] Clean system cache...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1

echo [Step4] Clean network cache...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1

echo [Completed] Quick memory release completed!
echo.

REM Display memory status after release
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set new_free_memory=%%a
set /a new_used_memory=%total_memory%-%new_free_memory%
set /a new_memory_usage_percent=%new_used_memory%*100/%total_memory%
set /a new_free_memory_gb=%new_free_memory%/1024/1024

echo [Result] Memory usage after release: %new_memory_usage_percent%%%
echo [Result] Available memory after release: %new_free_memory_gb% GB

if %new_memory_usage_percent% lss %memory_usage_percent% (
    set /a memory_saved=%memory_usage_percent%-%new_memory_usage_percent%
    set /a memory_freed_gb=%new_free_memory_gb%-%free_memory_gb%
    echo [Success] Memory usage reduced by !memory_saved! percentage points
    echo [Success] Released approximately !memory_freed_gb! GB memory
) else (
    echo [TIP] Memory usage not significantly improved
)

echo.
echo [Completed] Press any key to return to main menu...
pause >nul
goto :main_menu

:system_status
cls
echo ==========================================
echo        System Status Information
echo ==========================================
echo.

echo [System Information]
for /f "tokens=2 delims=:" %%a in ('wmic OS get Caption /value ^| find "="') do echo     Operating System: %%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get Version /value ^| find "="') do echo     System Version: %%a
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do (
    set total_physical=%%a
    set /a total_gb=!total_physical!/1024/1024/1024
    echo     Physical Memory: !total_gb! GB
)
echo.

echo [Memory Status]
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a

set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
set /a total_memory_gb=%total_memory%/1024/1024
set /a free_memory_gb=%free_memory%/1024/1024
set /a used_memory_gb=%used_memory%/1024/1024

echo     Total Memory: %total_memory_gb% GB
echo     Used Memory: %used_memory_gb% GB
echo     Free Memory: %free_memory_gb% GB
echo     Usage Rate: %memory_usage_percent%%%

if %memory_usage_percent% gtr 90 (
    echo     Status: [CRITICAL WARNING] Memory usage too high!
) else if %memory_usage_percent% gtr 80 (
    echo     Status: [WARNING] Memory usage high
) else if %memory_usage_percent% gtr 70 (
    echo     Status: [NOTICE] Memory usage elevated
) else (
    echo     Status: [NORMAL] Memory usage normal
)
echo.

echo [CPU Status]
for /f "tokens=2 delims=:" %%a in ('wmic cpu get Name /value ^| find "="') do echo     Processor: %%a
for /f "skip=1 tokens=2 delims=," %%a in ('wmic cpu get LoadPercentage /format:csv') do (
    if not "%%a"=="" (
        echo     CPU Usage: %%a%%
        if %%a gtr 90 (
            echo     Status: [WARNING] CPU usage too high
        ) else if %%a gtr 70 (
            echo     Status: [NOTICE] CPU usage elevated
        ) else (
            echo     Status: [NORMAL] CPU usage normal
        )
    )
)
echo.

echo [Key Service Status]
for %%s in ("SysMain" "WSearch" "DiagTrack" "dmwappushservice") do (
    for /f "tokens=4" %%a in ('sc query %%s ^| find "STATE"') do (
        echo     %%s: %%a
    )
)
echo.

echo [Startup Items Information]
for /f %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" 2^>nul ^| find /c "REG_"') do echo     Current User Startup Items: %%a items
for /f %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" 2^>nul ^| find /c "REG_"') do echo     System Startup Items: %%a items
echo.

echo ==========================================
echo [Completed] Press any key to return to main menu...
pause >nul
goto :main_menu

:end
cls
echo ==========================================
echo    Windows 10 Memory Optimization Tool
echo ==========================================
echo.
echo [Thanks] Thank you for using this tool!
echo [TIP] If the tool is helpful, recommend running regularly to maintain optimal system performance
echo.
echo [Recommended Usage Frequency]
echo     - Process cleanup: Daily or when feeling sluggish
echo     - Memory monitoring: During long computer use
echo     - System configuration: Only needs to run once
echo     - Quick release: Use in emergency situations
echo.
echo Press any key to exit program...
pause >nul
exit /b
