@echo off
setlocal enabledelayedexpansion

REM Check admin privileges
net session >nul 2>&1
if not %errorLevel% == 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

:main_menu
cls
echo ==========================================
echo    Windows 10 Memory Optimization Tool
echo ==========================================
echo.
echo Select an operation:
echo.
echo 1. Process Cleanup and Memory Optimization
echo 2. Quick Memory Release
echo 3. System Status
echo 4. Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto process_cleanup
if "%choice%"=="2" goto quick_release
if "%choice%"=="3" goto system_status
if "%choice%"=="4" goto script_end
echo Invalid choice. Please try again.
timeout /t 2 >nul
goto main_menu

:process_cleanup
cls
echo ==========================================
echo      Process Cleanup and Optimization
echo ==========================================
echo.

REM Define target processes
set "PROCESSES=chrome.exe msedge.exe firefox.exe qq.exe wechat.exe douyin.exe"

echo Checking and terminating target processes...
echo.

set count=0
for %%p in (%PROCESSES%) do (
    tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
    if not errorlevel 1 (
        echo Terminating %%p...
        taskkill /f /im "%%p" >nul 2>&1
        if not errorlevel 1 (
            echo   [OK] %%p terminated
            set /a count+=1
        ) else (
            echo   [FAIL] Could not terminate %%p
        )
    ) else (
        echo   [INFO] %%p not running
    )
)

echo.
echo Terminated %count% processes.
echo.

echo Cleaning temporary files...
del /f /q "%temp%\*.*" >nul 2>&1
del /f /q "%tmp%\*.*" >nul 2>&1
del /f /q "C:\Windows\Temp\*.*" >nul 2>&1
echo Temporary files cleaned.

echo.
echo Clearing system caches...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
echo System caches cleared.

echo.
echo Checking memory usage...
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value 2^>nul ^| find "="') do set total_mem=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value 2^>nul ^| find "="') do set free_mem=%%a

if defined total_mem if defined free_mem (
    set /a used_mem=%total_mem%-%free_mem%
    set /a usage_percent=%used_mem%*100/%total_mem%
    echo Current memory usage: %usage_percent%%%
    
    if %usage_percent% gtr 80 (
        echo High memory usage detected. Performing additional cleanup...
        powershell -Command "[System.GC]::Collect()" >nul 2>&1
        echo Memory optimization completed.
    )
) else (
    echo Could not retrieve memory information.
)

echo.
echo Process cleanup completed!
echo Press any key to return to menu...
pause >nul
goto main_menu

:quick_release
cls
echo ==========================================
echo         Quick Memory Release
echo ==========================================
echo.

echo Performing quick memory release...
echo.

echo Step 1: Garbage collection...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
echo   Memory garbage collection completed.

echo Step 2: Clearing temporary files...
del /f /q "%temp%\*.*" >nul 2>&1
del /f /q "%tmp%\*.*" >nul 2>&1
echo   Temporary files cleared.

echo Step 3: Flushing network caches...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
echo   Network caches flushed.

echo Step 4: Working set optimization...
powershell -Command "Get-Process | ForEach-Object { try { $_.ProcessorAffinity = $_.ProcessorAffinity } catch {} }" >nul 2>&1
echo   Working set optimization completed.

echo.
echo Quick memory release completed!
echo Press any key to return to menu...
pause >nul
goto main_menu

:system_status
cls
echo ==========================================
echo        System Status Information
echo ==========================================
echo.

echo Gathering system information...
echo.

echo [Operating System]
for /f "tokens=2 delims=:" %%a in ('wmic OS get Caption /value 2^>nul ^| find "="') do echo   OS: %%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get Version /value 2^>nul ^| find "="') do echo   Version: %%a

echo.
echo [Memory Information]
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value 2^>nul ^| find "="') do (
    set total_physical=%%a
    set /a total_gb=!total_physical!/1024/1024/1024
    echo   Physical Memory: !total_gb! GB
)

for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value 2^>nul ^| find "="') do set total_mem=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value 2^>nul ^| find "="') do set free_mem=%%a

if defined total_mem if defined free_mem (
    set /a used_mem=%total_mem%-%free_mem%
    set /a usage_percent=%used_mem%*100/%total_mem%
    set /a free_gb=%free_mem%/1024/1024
    set /a used_gb=%used_mem%/1024/1024
    
    echo   Used Memory: !used_gb! GB
    echo   Free Memory: !free_gb! GB
    echo   Usage Percentage: !usage_percent!%%
    
    if !usage_percent! gtr 90 (
        echo   Status: CRITICAL - Very high memory usage
    ) else if !usage_percent! gtr 80 (
        echo   Status: WARNING - High memory usage
    ) else if !usage_percent! gtr 70 (
        echo   Status: CAUTION - Elevated memory usage
    ) else (
        echo   Status: NORMAL - Memory usage is acceptable
    )
) else (
    echo   Could not retrieve memory usage information.
)

echo.
echo [CPU Information]
for /f "tokens=2 delims=:" %%a in ('wmic cpu get Name /value 2^>nul ^| find "="') do echo   Processor: %%a

echo.
echo [Key Services Status]
for %%s in (SysMain WSearch DiagTrack) do (
    for /f "tokens=4" %%a in ('sc query %%s 2^>nul ^| find "STATE"') do (
        echo   %%s: %%a
    )
)

echo.
echo Press any key to return to menu...
pause >nul
goto main_menu

:script_end
cls
echo ==========================================
echo    Windows 10 Memory Optimization Tool
echo ==========================================
echo.
echo Thank you for using this tool!
echo.
echo Recommended usage:
echo - Run Process Cleanup daily or when system feels slow
echo - Use Quick Memory Release for immediate relief
echo - Check System Status to monitor performance
echo.
echo Press any key to exit...
pause >nul
exit /b 0
