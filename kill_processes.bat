@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title 进程强制终止工具 - 管理员模式

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [√] 已获取管理员权限
    goto :main
) else (
    echo [!] 需要管理员权限运行此脚本
    echo [*] 正在请求管理员权限...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~dpnx0\"' -Verb RunAs"
    exit /b
)

:main
cls
echo ==========================================
echo          进程强制终止工具 v1.0
echo ==========================================
echo.

REM ====== 在这里配置要终止的程序列表 ======
REM 请根据需要修改以下程序列表
REM 每个程序用空格分隔，确保包含.exe扩展名

set "TARGET_PROCESSES=backgroundTaskHost.exe msedge.exe PotPlayerMini64.exe wpscloudsvr.exe taskmgr.exe 360se.exe SGTool.exe qdownloader32.exe SeAppService.exe QQLive.exe hardwarecheck.exe QQLiveService.exe TMPThumb.exe QQLiveBrowser.exe wpscloudsvr.exe sesvr.exe crashpad_handler.exe quark.exe 360CloudEnterpriseSync.exe KuGou.exe wps.exe qq.exe douyin.exe douyin_tray.exe i4Tools.exe updater.exe i4Service.exe i4ToolsService.exe sppsvc.exe chrome.exe TMPThumb.exe MOM.exe crashpad_handler.exe"

REM 常用浏览器（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% chrome.exe firefox.exe msedge.exe"

REM 办公软件（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% winword.exe excel.exe powerpnt.exe"

REM 聊天软件（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% qq.exe wechat.exe"

REM 游戏平台（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% steam.exe"

REM 媒体播放器（取消注释来启用）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% vlc.exe wmplayer.exe"

REM 自定义程序（请在下面添加你需要终止的其他程序）
REM set "TARGET_PROCESSES=%TARGET_PROCESSES% your_program.exe"

REM ============================================

echo [*] 目标进程列表:
set count=0
for %%p in (%TARGET_PROCESSES%) do (
    set /a count+=1
    echo     !count!. %%p
)
echo.

if %count%==0 (
    echo [!] 错误: 没有配置任何目标进程
    goto :end
)

echo ==========================================
echo [*] 开始强制终止进程...
echo ==========================================
echo.

set "killed_count=0"
set "not_found_count=0"
set "failed_count=0"

for %%p in (%TARGET_PROCESSES%) do (
    echo [处理] 检查进程: %%p
    
    REM 检查进程是否存在
    tasklist /fi "imagename eq %%p" 2>nul | find /i /c "%%p" >nul
    if errorlevel 1 (
        echo     └─ [o] 进程未运行
        set /a not_found_count+=1
    ) else (
        echo     └─ [!] 发现运行中的进程，正在强制终止...
        
        REM 使用多种方法确保进程被终止
        
        REM 方法1: 使用taskkill强制终止
        taskkill /f /im "%%p" >nul 2>&1
        
        REM 方法2: 使用wmic强制终止（备用方法）
        wmic process where "name='%%p'" delete >nul 2>&1
        
        REM 等待一秒后检查是否成功终止
        timeout /t 1 /nobreak >nul
        
        REM 验证进程是否已终止
        tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
        if errorlevel 1 (
            echo     └─ [√] 成功终止进程
            set /a killed_count+=1
        ) else (
            echo     └─ [×] 终止失败，进程仍在运行
            set /a failed_count+=1
            
            REM 最后尝试：使用PowerShell强制终止
            echo     └─ [*] 尝试PowerShell强制终止...
            powershell -Command "Get-Process -Name '%%~np' -ErrorAction SilentlyContinue | Stop-Process -Force" >nul 2>&1
            
            timeout /t 1 /nobreak >nul
            tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
            if errorlevel 1 (
                echo     └─ [√] PowerShell终止成功
                set /a killed_count+=1
                set /a failed_count-=1
            ) else (
                echo     └─ [×] 所有方法均失败
            )
        )
    )
    echo.
)

echo ==========================================
echo [*] 操作完成统计:
echo ==========================================
echo [√] 成功终止: %killed_count% 个进程
echo [o] 未发现运行: %not_found_count% 个进程  
echo [×] 终止失败: %failed_count% 个进程
echo [*] 总计处理: %count% 个进程
echo ==========================================

if %failed_count% gtr 0 (
    echo.
    echo [!] 注意: 有 %failed_count% 个进程终止失败
    echo [*] 可能原因:
    echo     1. 进程受到系统保护
    echo     2. 进程具有更高权限
    echo     3. 进程名称不正确
    echo     4. 进程正在被其他程序使用
)

echo.
echo ==========================================
echo [*] 开始系统垃圾和缓存清理...
echo ==========================================
echo.

REM 清理临时文件
echo [清理] 正在清理临时文件...
del /f /s /q "%temp%\*.*" >nul 2>&1
del /f /s /q "%tmp%\*.*" >nul 2>&1
for /d %%d in ("%temp%\*") do rd /s /q "%%d" >nul 2>&1
for /d %%d in ("%tmp%\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 临时文件清理完成

REM 清理系统临时文件
echo [清理] 正在清理系统临时文件...
del /f /s /q "C:\Windows\Temp\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Temp\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 系统临时文件清理完成

REM 清理预读文件
echo [清理] 正在清理预读文件...
del /f /s /q "C:\Windows\Prefetch\*.*" >nul 2>&1
echo     └─ [√] 预读文件清理完成

REM 清理回收站
echo [清理] 正在清理回收站...
rd /s /q "C:\$Recycle.Bin" >nul 2>&1
echo     └─ [√] 回收站清理完成

REM 清理DNS缓存
echo [清理] 正在清理DNS缓存...
ipconfig /flushdns >nul 2>&1
echo     └─ [√] DNS缓存清理完成

REM 清理ARP缓存
echo [清理] 正在清理ARP缓存...
arp -d * >nul 2>&1
echo     └─ [√] ARP缓存清理完成

REM 清理NetBIOS缓存
echo [清理] 正在清理NetBIOS缓存...
nbtstat -R >nul 2>&1
nbtstat -RR >nul 2>&1
echo     └─ [√] NetBIOS缓存清理完成

REM 清理浏览器缓存目录
echo [清理] 正在清理浏览器缓存...

REM Chrome缓存
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM Edge缓存
if exist "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM 360浏览器缓存
if exist "%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\360Chrome\Chrome\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

REM QQ浏览器缓存
if exist "%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache" (
    del /f /s /q "%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache\*.*" >nul 2>&1
    for /d %%d in ("%LOCALAPPDATA%\Tencent\QQBrowser\User Data\Default\Cache\*") do rd /s /q "%%d" >nul 2>&1
)

echo     └─ [√] 浏览器缓存清理完成

REM 清理Windows更新缓存
echo [清理] 正在清理Windows更新缓存...
del /f /s /q "C:\Windows\SoftwareDistribution\Download\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\SoftwareDistribution\Download\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] Windows更新缓存清理完成

REM 清理字体缓存
echo [清理] 正在清理字体缓存...
del /f /s /q "C:\Windows\ServiceProfiles\LocalService\AppData\Local\FontCache\*.*" >nul 2>&1
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Fonts\*.*" >nul 2>&1
echo     └─ [√] 字体缓存清理完成

REM 清理缩略图缓存
echo [清理] 正在清理缩略图缓存...
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\thumbcache_*.db" >nul 2>&1
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\iconcache_*.db" >nul 2>&1
echo     └─ [√] 缩略图缓存清理完成

REM 清理日志文件
echo [清理] 正在清理系统日志文件...
del /f /s /q "C:\Windows\Logs\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Logs\*") do rd /s /q "%%d" >nul 2>&1
del /f /s /q "C:\Windows\Debug\*.*" >nul 2>&1
for /d %%d in ("C:\Windows\Debug\*") do rd /s /q "%%d" >nul 2>&1
echo     └─ [√] 系统日志文件清理完成

REM 清理内存转储文件
echo [清理] 正在清理内存转储文件...
del /f /q "C:\Windows\MEMORY.DMP" >nul 2>&1
del /f /q "C:\Windows\Minidump\*.*" >nul 2>&1
echo     └─ [√] 内存转储文件清理完成

REM 清理空文件夹
echo [清理] 正在清理空文件夹...
for /f "delims=" %%d in ('dir "C:\Windows\Temp" /ad /b 2^>nul') do rd "C:\Windows\Temp\%%d" >nul 2>&1
for /f "delims=" %%d in ('dir "%temp%" /ad /b 2^>nul') do rd "%temp%\%%d" >nul 2>&1
echo     └─ [√] 空文件夹清理完成

echo.
echo ==========================================
echo [*] 开始内存优化和防卡死处理...
echo ==========================================
echo.

REM 检查当前内存使用情况
echo [检测] 正在检查内存使用情况...
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set total_memory=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set free_memory=%%a
set /a used_memory=%total_memory%-%free_memory%
set /a memory_usage_percent=%used_memory%*100/%total_memory%
echo     └─ [*] 内存使用率: %memory_usage_percent%%%
echo.

REM 如果内存使用率超过80%，执行紧急清理
if %memory_usage_percent% gtr 80 (
    echo [警告] 内存使用率过高（%memory_usage_percent%%%），执行紧急优化...

    REM 强制释放工作集内存
    echo [优化] 正在强制释放工作集内存...
    powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
    echo     └─ [√] 工作集内存释放完成

    REM 清理系统文件缓存
    echo [优化] 正在清理系统文件缓存...
    powershell -Command "Clear-RecycleBin -Force -ErrorAction SilentlyContinue" >nul 2>&1
    echo     └─ [√] 系统文件缓存清理完成

    REM 释放待机内存
    echo [优化] 正在释放待机内存...
    powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; $api::SetProcessWorkingSetSize((Get-Process -Id $pid).Handle, -1, -1)" >nul 2>&1
    echo     └─ [√] 待机内存释放完成
)

REM 优化虚拟内存设置
echo [优化] 正在优化虚拟内存设置...
REM 设置虚拟内存为系统管理大小
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys 0 0" /f >nul 2>&1
echo     └─ [√] 虚拟内存设置优化完成

REM 禁用内存压缩（可选，适用于内存充足的系统）
echo [优化] 正在优化内存压缩设置...
powershell -Command "Disable-MMAgent -MemoryCompression -ErrorAction SilentlyContinue" >nul 2>&1
echo     └─ [√] 内存压缩设置优化完成

REM 优化系统缓存策略
echo [优化] 正在优化系统缓存策略...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 1 /f >nul 2>&1
echo     └─ [√] 系统缓存策略优化完成

REM 禁用不必要的Windows服务以释放内存
echo [优化] 正在禁用高内存占用服务...
sc config "SysMain" start= disabled >nul 2>&1
sc config "WSearch" start= disabled >nul 2>&1
sc config "DiagTrack" start= disabled >nul 2>&1
sc config "dmwappushservice" start= disabled >nul 2>&1
sc config "MapsBroker" start= disabled >nul 2>&1
sc config "lfsvc" start= disabled >nul 2>&1
echo     └─ [√] 高内存占用服务禁用完成

REM 停止当前运行的高内存占用服务
echo [优化] 正在停止高内存占用服务...
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
net stop "dmwappushservice" >nul 2>&1
echo     └─ [√] 高内存占用服务停止完成

REM 清理内存映射文件
echo [优化] 正在清理内存映射文件...
del /f /q "%SystemRoot%\System32\config\systemprofile\AppData\Local\Microsoft\Windows\WebCache\*.*" >nul 2>&1
echo     └─ [√] 内存映射文件清理完成

REM 优化进程优先级设置
echo [优化] 正在优化进程优先级设置...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 38 /f >nul 2>&1
echo     └─ [√] 进程优先级设置优化完成

REM 再次检查内存使用情况
echo.
echo [检测] 正在重新检查内存使用情况...
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set new_free_memory=%%a
set /a new_used_memory=%total_memory%-%new_free_memory%
set /a new_memory_usage_percent=%new_used_memory%*100/%total_memory%
echo     └─ [*] 优化后内存使用率: %new_memory_usage_percent%%%

if %new_memory_usage_percent% lss %memory_usage_percent% (
    set /a memory_saved=%memory_usage_percent%-%new_memory_usage_percent%
    echo     └─ [√] 内存使用率降低了 !memory_saved! 个百分点
) else (
    echo     └─ [!] 内存使用率未明显改善，建议重启系统
)

echo.
echo ==========================================
echo [*] 系统清理完成统计:
echo ==========================================
echo [√] 临时文件清理
echo [√] 系统临时文件清理
echo [√] 预读文件清理
echo [√] 回收站清理
echo [√] DNS/ARP/NetBIOS缓存清理
echo [√] 浏览器缓存清理
echo [√] Windows更新缓存清理
echo [√] 字体缓存清理
echo [√] 缩略图缓存清理
echo [√] 系统日志文件清理
echo [√] 内存转储文件清理
echo [√] 磁盘清理
echo [√] 空文件夹清理
echo [√] 内存优化和防卡死处理
echo ==========================================

:end
echo.
echo 按任意键退出...
pause >nul
exit /b 