@echo off
setlocal enabledelayedexpansion
title Advanced Windows 10 Optimization Tool - Professional Edition

REM Check admin privileges
net session >nul 2>&1
if not %errorLevel% == 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

cls
echo ==========================================
echo   Advanced Windows 10 Optimization Tool
echo           Professional Edition
echo ==========================================
echo.
echo WARNING: This is an advanced optimization script
echo It includes aggressive optimizations that may affect system functionality
echo Only use if you understand the implications
echo.
echo Advanced optimizations include:
echo - Deep registry modifications
echo - Advanced service management
echo - Hardware-specific optimizations
echo - Gaming performance tweaks
echo - Developer environment optimizations
echo.

set /p confirm="Do you want to proceed with advanced optimizations? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled by user.
    pause
    exit /b 0
)

echo.
echo Creating comprehensive system restore point...
powershell -Command "Checkpoint-Computer -Description 'Advanced Win10 Optimization - Professional' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
echo.

echo [ADVANCED] Detecting system configuration...
REM Detect system specifications
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set total_physical=%%a
for /f "tokens=2 delims=:" %%a in ('wmic cpu get NumberOfCores /value ^| find "="') do set cpu_cores=%%a
for /f "tokens=2 delims=:" %%a in ('wmic cpu get NumberOfLogicalProcessors /value ^| find "="') do set logical_processors=%%a
set /a total_gb=!total_physical!/1024/1024/1024

echo System detected: %total_gb%GB RAM, %cpu_cores% cores, %logical_processors% logical processors
echo.

echo [ADVANCED] Applying deep registry optimizations...
REM Advanced memory management
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v FeatureSettings /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v FeatureSettingsOverride /t REG_DWORD /d 3 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v FeatureSettingsOverrideMask /t REG_DWORD /d 3 /f >nul 2>&1

REM Advanced CPU scheduling
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v IRQ8Priority /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v IRQ16Priority /t REG_DWORD /d 2 /f >nul 2>&1

REM Advanced I/O optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v IoPageLockLimit /t REG_DWORD /d 983040 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\lanmanserver\parameters" /v IRPStackSize /t REG_DWORD /d 32 /f >nul 2>&1

REM Gaming optimizations
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "GPU Priority" /t REG_DWORD /d 8 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v Priority /t REG_DWORD /d 6 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Scheduling Category" /t REG_SZ /d High /f >nul 2>&1

REM Advanced network optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TcpAckFrequency /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TCPNoDelay /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v Tcp1323Opts /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v DefaultTTL /t REG_DWORD /d 64 /f >nul 2>&1

REM Advanced file system optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsDisable8dot3NameCreation /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsEncryptionService /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsMemoryUsage /t REG_DWORD /d 2 /f >nul 2>&1

echo Advanced registry optimizations applied.
echo.

echo [ADVANCED] Configuring hardware-specific optimizations...
REM Configure based on detected hardware
if %total_gb% geq 16 (
    echo Applying optimizations for high-memory system...
    REM High memory system optimizations
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v SystemPages /t REG_DWORD /d 4294967295 /f >nul 2>&1
    
    REM Disable memory compression for high-memory systems
    powershell -Command "Disable-MMAgent -MemoryCompression" >nul 2>&1
    
    REM Configure large page support
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargePageMinimum /t REG_DWORD /d 0 /f >nul 2>&1
)

if %logical_processors% geq 8 (
    echo Applying optimizations for multi-core system...
    REM Multi-core optimizations
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v ThreadDpcEnable /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v DpcWatchdogProfileOffset /t REG_DWORD /d 1 /f >nul 2>&1
    
    REM Advanced processor scheduling
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v ConvertibleSlateMode /t REG_DWORD /d 0 /f >nul 2>&1
)

echo Hardware-specific optimizations applied.
echo.

echo [ADVANCED] Applying aggressive service optimizations...
REM Additional services to disable for maximum performance
sc config "Themes" start= disabled >nul 2>&1
sc config "TabletInputService" start= disabled >nul 2>&1
sc config "WerSvc" start= disabled >nul 2>&1
sc config "Spooler" start= demand >nul 2>&1
sc config "Fax" start= disabled >nul 2>&1
sc config "WMPNetworkSvc" start= disabled >nul 2>&1
sc config "RemoteRegistry" start= disabled >nul 2>&1
sc config "RemoteAccess" start= disabled >nul 2>&1
sc config "WinRM" start= disabled >nul 2>&1
sc config "PolicyAgent" start= demand >nul 2>&1

REM Stop services immediately
net stop "Themes" >nul 2>&1
net stop "TabletInputService" >nul 2>&1
net stop "WerSvc" >nul 2>&1

echo Aggressive service optimizations applied.
echo.

echo [ADVANCED] Configuring power management...
REM Advanced power settings
powercfg -setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
powercfg -change -monitor-timeout-ac 0 >nul 2>&1
powercfg -change -monitor-timeout-dc 0 >nul 2>&1
powercfg -change -disk-timeout-ac 0 >nul 2>&1
powercfg -change -disk-timeout-dc 0 >nul 2>&1
powercfg -change -standby-timeout-ac 0 >nul 2>&1
powercfg -change -standby-timeout-dc 0 >nul 2>&1

REM Disable USB selective suspend
powercfg -setacvalueindex SCHEME_CURRENT 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 0 >nul 2>&1

echo Power management optimized.
echo.

echo [ADVANCED] Applying developer environment optimizations...
REM Developer-specific optimizations
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v SmartScreenEnabled /t REG_SZ /d Off /f >nul 2>&1
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\AppHost" /v EnableWebContentEvaluation /t REG_DWORD /d 0 /f >nul 2>&1

REM Disable Windows Defender for development (CAUTION: Only for isolated dev environments)
REM reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender" /v DisableAntiSpyware /t REG_DWORD /d 1 /f >nul 2>&1

REM Enable long path support
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v LongPathsEnabled /t REG_DWORD /d 1 /f >nul 2>&1

echo Developer optimizations applied.
echo.

echo [ADVANCED] Configuring advanced startup optimizations...
REM Boot optimization
bcdedit /set useplatformclock true >nul 2>&1
bcdedit /set disabledynamictick yes >nul 2>&1
bcdedit /set tscsyncpolicy Enhanced >nul 2>&1

REM Advanced boot settings
bcdedit /set nx OptIn >nul 2>&1
bcdedit /set bootmenupolicy Legacy >nul 2>&1

echo Startup optimizations applied.
echo.

echo [ADVANCED] Performing deep system cleanup...
REM Advanced cleanup
powershell -Command "Get-ChildItem -Path 'C:\Windows\Logs' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue" >nul 2>&1
powershell -Command "Get-ChildItem -Path 'C:\Windows\Panther' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue" >nul 2>&1
powershell -Command "Get-ChildItem -Path 'C:\Windows\SoftwareDistribution\Download' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue" >nul 2>&1

REM Clear event logs
for /f "tokens=*" %%G in ('wevtutil.exe el') DO (call :do_clear "%%G")
goto :skip_clear
:do_clear
wevtutil.exe cl %1 >nul 2>&1
goto :eof
:skip_clear

echo Deep system cleanup completed.
echo.

echo [ADVANCED] Finalizing optimizations...
REM Final optimizations
sfc /scannow >nul 2>&1
dism /online /cleanup-image /restorehealth >nul 2>&1

echo.
echo ==========================================
echo    ADVANCED OPTIMIZATION COMPLETED
echo ==========================================
echo.
echo CRITICAL NOTES:
echo - Advanced optimizations have been applied
echo - Some changes may affect system stability
echo - Monitor system behavior after restart
echo - Use system restore if issues occur
echo - Windows Defender remains enabled for security
echo.
echo Recommended next steps:
echo 1. Restart your computer
echo 2. Test all critical applications
echo 3. Monitor system performance
echo 4. Adjust settings if needed
echo.
echo ==========================================
echo.

set /p restart_choice="Restart computer now to complete advanced optimization? (Y/N): "
if /i "%restart_choice%"=="Y" (
    echo Restarting in 15 seconds...
    timeout /t 15 /nobreak
    shutdown /r /t 0
) else (
    echo Please restart manually to complete optimization.
    pause
)

endlocal
