@echo off
setlocal enabledelayedexpansion
title Ultimate Windows 10 Optimization Tool - Based on Best Practices

REM Check admin privileges
net session >nul 2>&1
if not %errorLevel% == 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

cls
echo ==========================================
echo   Ultimate Windows 10 Optimization Tool
echo ==========================================
echo.
echo Based on Microsoft VDI optimization guide and community best practices
echo This script will automatically optimize your Windows 10 system
echo.
echo Operations to be performed:
echo 1. System Status Check
echo 2. Registry Performance Optimizations
echo 3. Services Optimization
echo 4. Memory Management Optimization
echo 5. Network Performance Tuning
echo 6. UWP Apps Cleanup
echo 7. Scheduled Tasks Optimization
echo 8. Visual Effects Optimization
echo 9. Storage and Disk Optimization
echo 10. Final System Cleanup
echo.
echo ==========================================
echo.

REM Create system restore point
echo [STEP 1/10] Creating system restore point...
powershell -Command "Checkpoint-Computer -Description 'Ultimate Win10 Optimization' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
echo System restore point created successfully.
echo.

REM Get initial system status
echo [STEP 1/10] Checking initial system status...
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value ^| find "="') do set initial_total_mem=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set initial_free_mem=%%a
set /a initial_used_mem=%initial_total_mem%-%initial_free_mem%
set /a initial_usage_percent=%initial_used_mem%*100/%initial_total_mem%
set /a initial_free_gb=%initial_free_mem%/1024/1024
echo Initial memory usage: %initial_usage_percent%% (%initial_free_gb% GB free)
echo.

echo [STEP 2/10] Applying registry performance optimizations...
REM Network performance tuning (Microsoft recommended)
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DisableBandwidthThrottling /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v FileInfoCacheEntriesMax /t REG_DWORD /d 1024 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DirectoryCacheEntriesMax /t REG_DWORD /d 1024 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v FileNotFoundCacheEntriesMax /t REG_DWORD /d 2048 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters" /v DormantFileLimit /t REG_DWORD /d 256 /f >nul 2>&1

REM Memory management optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v LargeSystemCache /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v DisablePagingExecutive /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v ClearPageFileAtShutdown /t REG_DWORD /d 0 /f >nul 2>&1

REM Process scheduling optimization
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v Win32PrioritySeparation /t REG_DWORD /d 38 /f >nul 2>&1

REM System responsiveness
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v SystemResponsiveness /t REG_DWORD /d 10 /f >nul 2>&1

REM Disable NTFS last access time updates (performance boost)
reg add "HKLM\SYSTEM\CurrentControlSet\Control\FileSystem" /v NtfsDisableLastAccessUpdate /t REG_DWORD /d 1 /f >nul 2>&1

REM Disable Windows Error Reporting
reg add "HKLM\SOFTWARE\Microsoft\Windows\Windows Error Reporting" /v Disabled /t REG_DWORD /d 1 /f >nul 2>&1

REM Disable Customer Experience Improvement Program
reg add "HKLM\SOFTWARE\Microsoft\SQMClient\Windows" /v CEIPEnable /t REG_DWORD /d 0 /f >nul 2>&1

REM Disable Windows Defender real-time protection (optional - be careful)
REM reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection" /v DisableRealtimeMonitoring /t REG_DWORD /d 1 /f >nul 2>&1

echo Registry optimizations applied successfully.
echo.

echo [STEP 3/10] Optimizing Windows services...
REM Disable unnecessary services based on Microsoft VDI guide
sc config "SysMain" start= disabled >nul 2>&1
sc config "WSearch" start= disabled >nul 2>&1
sc config "DiagTrack" start= disabled >nul 2>&1
sc config "dmwappushservice" start= disabled >nul 2>&1
sc config "MapsBroker" start= disabled >nul 2>&1
sc config "lfsvc" start= disabled >nul 2>&1
sc config "SharedAccess" start= disabled >nul 2>&1
sc config "TrkWks" start= disabled >nul 2>&1
sc config "WbioSrvc" start= disabled >nul 2>&1
sc config "WMPNetworkSvc" start= disabled >nul 2>&1
sc config "XblAuthManager" start= disabled >nul 2>&1
sc config "XblGameSave" start= disabled >nul 2>&1
sc config "XboxNetApiSvc" start= disabled >nul 2>&1
sc config "XboxGipSvc" start= disabled >nul 2>&1

REM Stop running services
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
net stop "dmwappushservice" >nul 2>&1
net stop "MapsBroker" >nul 2>&1
net stop "lfsvc" >nul 2>&1

echo Windows services optimized successfully.
echo.

echo [STEP 4/10] Optimizing memory management...
REM Get system memory size for intelligent configuration
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set total_physical=%%a
set /a total_gb=!total_physical!/1024/1024/1024

REM Configure virtual memory based on system RAM
if !total_gb! geq 16 (
    set /a min_pagefile=!total_gb!*1024
    set /a max_pagefile=!total_gb!*1536
    echo Configuring virtual memory for 16GB+ system...
) else if !total_gb! geq 8 (
    set /a min_pagefile=!total_gb!*1536
    set /a max_pagefile=!total_gb!*2048
    echo Configuring virtual memory for 8-16GB system...
) else (
    set /a min_pagefile=!total_gb!*2048
    set /a max_pagefile=!total_gb!*3072
    echo Configuring virtual memory for less than 8GB system...
)

reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v PagingFiles /t REG_MULTI_SZ /d "C:\pagefile.sys !min_pagefile! !max_pagefile!" /f >nul 2>&1

REM Disable memory compression for systems with 8GB+ RAM
if !total_gb! geq 8 (
    echo Disabling memory compression for high-memory system...
    powershell -Command "Disable-MMAgent -MemoryCompression" >nul 2>&1
)

echo Memory management optimized successfully.
echo.

echo [STEP 5/10] Applying network performance tuning...
REM TCP/IP optimizations
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
netsh int tcp set global netdma=enabled >nul 2>&1

REM DNS optimizations
netsh interface ip set dns "Local Area Connection" static ******* primary >nul 2>&1
netsh interface ip add dns "Local Area Connection" ******* index=2 >nul 2>&1

echo Network performance tuning applied successfully.
echo.

echo [STEP 6/10] Cleaning up UWP applications...
REM Remove unnecessary UWP apps (based on community feedback)
powershell -Command "Get-AppxPackage *3dbuilder* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowsalarms* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowscommunicationsapps* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowscamera* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *officehub* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *skypeapp* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *getstarted* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *zunemusic* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowsmaps* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *solitairecollection* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingfinance* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *zunevideo* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingnews* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *onenote* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *people* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *windowsphone* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *photos* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingsports* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *soundrecorder* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *bingweather* | Remove-AppxPackage" >nul 2>&1
powershell -Command "Get-AppxPackage *xboxapp* | Remove-AppxPackage" >nul 2>&1

echo UWP applications cleanup completed successfully.
echo.

echo [STEP 7/10] Optimizing scheduled tasks...
REM Disable unnecessary scheduled tasks based on Microsoft VDI guide
schtasks /change /tn "Microsoft\Windows\Application Experience\Microsoft Compatibility Appraiser" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Application Experience\ProgramDataUpdater" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Autochk\Proxy" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\Consolidator" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\KernelCeipTask" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Customer Experience Improvement Program\UsbCeip" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\DiskDiagnostic\Microsoft-Windows-DiskDiagnosticDataCollector" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Maintenance\WinSAT" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Maps\MapsToastTask" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Maps\MapsUpdateTask" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Mobile Broadband Accounts\MNO Metadata Parser" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Power Efficiency Diagnostics\AnalyzeSystem" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\Windows Error Reporting\QueueReporting" /disable >nul 2>&1
schtasks /change /tn "Microsoft\Windows\WindowsUpdate\Scheduled Start" /disable >nul 2>&1

echo Scheduled tasks optimized successfully.
echo.

echo [STEP 8/10] Optimizing visual effects...
REM Disable visual effects for better performance
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v VisualFXSetting /t REG_DWORD /d 2 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v DragFullWindows /t REG_SZ /d 0 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop" /v MenuShowDelay /t REG_SZ /d 0 /f >nul 2>&1
reg add "HKCU\Control Panel\Desktop\WindowMetrics" /v MinAnimate /t REG_SZ /d 0 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ListviewAlphaSelect /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ListviewShadow /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v TaskbarAnimations /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\DWM" /v EnableAeroPeek /t REG_DWORD /d 0 /f >nul 2>&1

REM Disable transparency effects
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 0 /f >nul 2>&1

echo Visual effects optimized successfully.
echo.

echo [STEP 9/10] Optimizing storage and disk performance...
REM Disable Windows Search indexing for better disk performance
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowIndexingEncryptedStoresOrItems /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowSearchToUseLocation /t REG_DWORD /d 0 /f >nul 2>&1

REM Disable hibernation to free up disk space
powershell -Command "powercfg -h off" >nul 2>&1

REM Disable system restore on all drives except C: (optional)
REM vssadmin delete shadows /for=c: /all /quiet >nul 2>&1

REM Clean temporary files
del /q /f /s "%TEMP%\*" >nul 2>&1
del /q /f /s "C:\Windows\Temp\*" >nul 2>&1
del /q /f /s "C:\Windows\Prefetch\*" >nul 2>&1

REM Clear Windows Update cache
net stop wuauserv >nul 2>&1
del /q /f /s "C:\Windows\SoftwareDistribution\Download\*" >nul 2>&1
net start wuauserv >nul 2>&1

echo Storage and disk optimization completed successfully.
echo.

echo [STEP 10/10] Final system cleanup and optimization...
REM Flush DNS cache
ipconfig /flushdns >nul 2>&1

REM Reset network stack
netsh winsock reset >nul 2>&1

REM Clear system file cache
echo 3 > C:\Windows\System32\config\systemprofile\AppData\Local\Temp\freemem.txt
del C:\Windows\System32\config\systemprofile\AppData\Local\Temp\freemem.txt >nul 2>&1

REM Optimize system files
sfc /scannow >nul 2>&1

echo Final cleanup completed successfully.
echo.

REM Get final system status
echo Checking final system status...
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value ^| find "="') do set final_free_mem=%%a
set /a final_used_mem=%initial_total_mem%-%final_free_mem%
set /a final_usage_percent=%final_used_mem%*100/%initial_total_mem%
set /a final_free_gb=%final_free_mem%/1024/1024
set /a memory_freed=(%final_free_mem%-%initial_free_mem%)/1024
echo.

echo ==========================================
echo         OPTIMIZATION COMPLETED
echo ==========================================
echo.
echo BEFORE: Memory usage: %initial_usage_percent%% (%initial_free_gb% GB free)
echo AFTER:  Memory usage: %final_usage_percent%% (%final_free_gb% GB free)
echo FREED:  %memory_freed% MB of memory
echo.
echo Optimizations applied:
echo ✓ Registry performance tweaks
echo ✓ Services optimization
echo ✓ Memory management tuning
echo ✓ Network performance improvements
echo ✓ UWP apps cleanup
echo ✓ Scheduled tasks optimization
echo ✓ Visual effects optimization
echo ✓ Storage and disk optimization
echo ✓ System cleanup
echo.
echo IMPORTANT NOTES:
echo - A system restore point was created before optimization
echo - Some changes require a system restart to take full effect
echo - Windows Defender real-time protection remains enabled for security
echo - Critical system services were preserved
echo.
echo ==========================================
echo.

set /p restart_choice="Would you like to restart your computer now to complete the optimization? (Y/N): "
if /i "%restart_choice%"=="Y" (
    echo Restarting computer in 10 seconds...
    timeout /t 10 /nobreak
    shutdown /r /t 0
) else (
    echo Please restart your computer manually to complete the optimization.
    echo.
    echo Thank you for using Ultimate Windows 10 Optimization Tool!
    pause
)

endlocal
