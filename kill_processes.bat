@echo off
setlocal enabledelayedexpansion
title Windows 10 Auto Memory Optimization Tool

REM Check admin privileges
net session >nul 2>&1
if not %errorLevel% == 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

cls
echo ==========================================
echo   Windows 10 Auto Memory Optimization Tool
echo ==========================================
echo.
echo Starting automatic optimization process...
echo All operations will be performed automatically.
echo.
echo Operations to be performed:
echo 1. System Status Check
echo 2. Process Cleanup and Memory Optimization
echo 3. Quick Memory Release
echo 4. Final Status Report
echo.
echo ==========================================
echo.

REM Start automatic execution
goto auto_start

:auto_start
echo [STEP 1/4] Checking initial system status...
echo.
call :get_system_status "INITIAL"
echo.
echo Waiting 3 seconds before starting optimization...
timeout /t 3 /nobreak >nul

echo.
echo [STEP 2/4] Starting process cleanup and memory optimization...
echo.
call :process_cleanup_auto

echo.
echo [STEP 3/4] Performing quick memory release...
echo.
call :quick_release_auto

echo.
echo [STEP 4/4] Checking final system status...
echo.
call :get_system_status "FINAL"

echo.
echo ==========================================
echo    AUTOMATIC OPTIMIZATION COMPLETED
echo ==========================================
echo.
echo All operations have been completed successfully.
echo Your system has been optimized for better performance.
echo.
echo The window will close automatically in 10 seconds...
echo Press any key to close immediately.
timeout /t 10 >nul
exit /b 0

:process_cleanup_auto
echo ==========================================
echo      Process Cleanup and Optimization
echo ==========================================
echo.

REM Define target processes
set "PROCESSES=chrome.exe msedge.exe firefox.exe qq.exe wechat.exe douyin.exe backgroundTaskHost.exe PotPlayerMini64.exe wpscloudsvr.exe taskmgr.exe 360se.exe SGTool.exe qdownloader32.exe SeAppService.exe QQLive.exe hardwarecheck.exe QQLiveService.exe TMPThumb.exe QQLiveBrowser.exe sesvr.exe crashpad_handler.exe quark.exe 360CloudEnterpriseSync.exe KuGou.exe wps.exe"

echo Checking and terminating target processes...
echo.

set count=0
for %%p in (%PROCESSES%) do (
    tasklist /fi "imagename eq %%p" 2>nul | find /i "%%p" >nul
    if not errorlevel 1 (
        echo Terminating %%p...
        taskkill /f /im "%%p" >nul 2>&1
        if not errorlevel 1 (
            echo   [OK] %%p terminated
            set /a count+=1
        ) else (
            echo   [FAIL] Could not terminate %%p
        )
    ) else (
        echo   [INFO] %%p not running
    )
)

echo.
echo Terminated %count% processes.
echo.

echo Cleaning temporary files...
del /f /q "%temp%\*.*" >nul 2>&1
del /f /q "%tmp%\*.*" >nul 2>&1
del /f /q "C:\Windows\Temp\*.*" >nul 2>&1
del /f /q "C:\Windows\Prefetch\*.*" >nul 2>&1
echo Temporary files cleaned.

echo.
echo Clearing system caches...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
nbtstat -R >nul 2>&1
echo System caches cleared.

echo.
echo Optimizing system services...
net stop "SysMain" >nul 2>&1
net stop "WSearch" >nul 2>&1
net stop "DiagTrack" >nul 2>&1
echo Non-essential services optimized.

echo.
echo Checking memory usage...
for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value 2^>nul ^| find "="') do set total_mem=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value 2^>nul ^| find "="') do set free_mem=%%a

if defined total_mem if defined free_mem (
    set /a used_mem=%total_mem%-%free_mem%
    set /a usage_percent=%used_mem%*100/%total_mem%
    echo Current memory usage: %usage_percent%%%

    if %usage_percent% gtr 80 (
        echo High memory usage detected. Performing additional cleanup...
        powershell -Command "[System.GC]::Collect()" >nul 2>&1
        echo Memory optimization completed.
    ) else (
        echo Memory usage is acceptable.
    )
) else (
    echo Could not retrieve memory information.
)

echo.
echo Process cleanup completed!
return

:quick_release_auto
echo ==========================================
echo         Quick Memory Release
echo ==========================================
echo.

echo Performing quick memory release...
echo.

echo Step 1: Force garbage collection...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >nul 2>&1
echo   Memory garbage collection completed.

echo Step 2: Clearing remaining temporary files...
del /f /q "%temp%\*.*" >nul 2>&1
del /f /q "%tmp%\*.*" >nul 2>&1
del /f /q "C:\Windows\Temp\*.*" >nul 2>&1
echo   Temporary files cleared.

echo Step 3: Flushing network caches...
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1
nbtstat -R >nul 2>&1
echo   Network caches flushed.

echo Step 4: Working set optimization...
powershell -Command "Get-Process | ForEach-Object { try { $_.ProcessorAffinity = $_.ProcessorAffinity } catch {} }" >nul 2>&1
echo   Working set optimization completed.

echo Step 5: Memory defragmentation...
powershell -Command "$api = Add-Type -MemberDefinition '[DllImport(\"kernel32.dll\")] public static extern int SetProcessWorkingSetSize(IntPtr handle, int min, int max);' -Name Win32 -PassThru; Get-Process | ForEach-Object { try { $api::SetProcessWorkingSetSize($_.Handle, -1, -1) } catch {} }" >nul 2>&1
echo   Memory defragmentation completed.

echo.
echo Quick memory release completed!
return

:get_system_status
set status_type=%~1
echo ==========================================
echo        System Status Information (%status_type%)
echo ==========================================
echo.

echo Gathering system information...
echo.

echo [Operating System]
for /f "tokens=2 delims=:" %%a in ('wmic OS get Caption /value 2^>nul ^| find "="') do echo   OS: %%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get Version /value 2^>nul ^| find "="') do echo   Version: %%a

echo.
echo [Memory Information]
for /f "tokens=2 delims=:" %%a in ('wmic computersystem get TotalPhysicalMemory /value 2^>nul ^| find "="') do (
    set total_physical=%%a
    set /a total_gb=!total_physical!/1024/1024/1024
    echo   Physical Memory: !total_gb! GB
)

for /f "tokens=2 delims=:" %%a in ('wmic OS get TotalVisibleMemorySize /value 2^>nul ^| find "="') do set total_mem=%%a
for /f "tokens=2 delims=:" %%a in ('wmic OS get FreePhysicalMemory /value 2^>nul ^| find "="') do set free_mem=%%a

if defined total_mem if defined free_mem (
    set /a used_mem=%total_mem%-%free_mem%
    set /a usage_percent=%used_mem%*100/%total_mem%
    set /a free_gb=%free_mem%/1024/1024
    set /a used_gb=%used_mem%/1024/1024

    echo   Used Memory: !used_gb! GB
    echo   Free Memory: !free_gb! GB
    echo   Usage Percentage: !usage_percent!%%

    if "%status_type%"=="INITIAL" (
        set initial_usage=!usage_percent!
        echo   Status: %status_type% measurement
    ) else if "%status_type%"=="FINAL" (
        if defined initial_usage (
            set /a improvement=!initial_usage!-!usage_percent!
            if !improvement! gtr 0 (
                echo   Status: IMPROVED - Memory usage reduced by !improvement! percentage points
            ) else (
                echo   Status: STABLE - Memory usage maintained
            )
        ) else (
            echo   Status: FINAL measurement
        )
    )

    if !usage_percent! gtr 90 (
        echo   Level: CRITICAL - Very high memory usage
    ) else if !usage_percent! gtr 80 (
        echo   Level: WARNING - High memory usage
    ) else if !usage_percent! gtr 70 (
        echo   Level: CAUTION - Elevated memory usage
    ) else (
        echo   Level: NORMAL - Memory usage is acceptable
    )
) else (
    echo   Could not retrieve memory usage information.
)

echo.
echo [CPU Information]
for /f "tokens=2 delims=:" %%a in ('wmic cpu get Name /value 2^>nul ^| find "="') do echo   Processor: %%a

echo.
echo [Key Services Status]
for %%s in (SysMain WSearch DiagTrack) do (
    for /f "tokens=4" %%a in ('sc query %%s 2^>nul ^| find "STATE"') do (
        echo   %%s: %%a
    )
)

echo.
return

REM This section is no longer needed as the script runs automatically
